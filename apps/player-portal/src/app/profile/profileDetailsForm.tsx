import { FC } from "react";
import { useTranslation } from "react-i18next";

import {
  Box,
  MenuItem,
  Select,
  TextField,
  Typography,
  Card,
  FormLabel,
  DatePicker,
  FormControl,
  Alert,
  CenteredLayout,
  Button,
  InputLabel,
  FormHelperText,
} from "@mio/ui";
import {
  PlayingExperience,
  PlayerGenders,
  CustomDate,
  CustomNumber,
  APIError,
  Player,
} from "@mio/helpers";

import { DataShape } from "./types";
import { usePlayerDetailsForm } from "./usePlayerDetailsForm";
import { Controller } from "react-hook-form";

type Props = {
  onSubmit: (data: DataShape) => void;
  loading?: boolean;
  serverError: APIError | null;
  playerProfile: Player;
};

export const ProfileDetailsForm: FC<Props> = ({
  onSubmit,
  loading,
  serverError,
  playerProfile,
}) => {
  const { t } = useTranslation();
  const {
    formMethods,
    dob,
    hasSubmitted,
    errors,
    age,
    requiresAnEmail,
    playingExperience,
    guardianDob,
    data,
  } = usePlayerDetailsForm(playerProfile);

  return (
    <CenteredLayout>
      <Box component="header" mb={2}>
        <header>
          <Typography variant="h3" component="h1">
            {t("profile.form.title")}
          </Typography>
        </header>
      </Box>

      <Box component="main">
        <Card sx={{ padding: 2 }}>
          <form onSubmit={formMethods.handleSubmit(onSubmit)}>
            <Box
              component="fieldset"
              sx={{ borderTop: "none", borderLeft: "none", borderRight: "none" }}
            >
              <Box component="legend" mb={3}>
                <Typography variant="h4" component="h2" mb={1}>
                  {t("profile.form.sections.player.title")}
                </Typography>
              </Box>

              <Box mb={2}>
                <TextField
                  fullWidth
                  label={<FormLabel>{t("profile.form.sections.player.first-name")}</FormLabel>}
                  id="firstName"
                  {...formMethods.register("firstName")}
                  helperText={(hasSubmitted && errors.firstName?.message) || ""}
                  error={hasSubmitted && !!errors.firstName?.message}
                  aria-invalid={hasSubmitted && !!errors.firstName?.message}
                />
              </Box>

              <Box mb={2}>
                <TextField
                  fullWidth
                  label={<FormLabel>{t("profile.form.sections.player.last-name")}</FormLabel>}
                  id="lastName"
                  {...formMethods.register("lastName")}
                  helperText={(hasSubmitted && errors.lastName?.message) || ""}
                  error={hasSubmitted && !!errors.lastName?.message}
                  aria-invalid={hasSubmitted && !!errors.lastName?.message}
                />
              </Box>

              <Box mb={3}>
                <DatePicker
                  label={<FormLabel>{t("profile.form.sections.player.dob")}</FormLabel>}
                  format="dd/MM/y"
                  openTo="year"
                  maxDate={new Date()}
                  value={dob}
                  onChange={(newValue) => {
                    formMethods.setValue("dob", CustomDate.pastOrEmpty(newValue), {
                      shouldDirty: true,
                      shouldTouch: true,
                      shouldValidate: true,
                    });
                  }}
                  slotProps={{
                    textField: {
                      helperText: (hasSubmitted && errors.dob?.message) || "",
                      error: hasSubmitted && !!errors.dob?.message,
                      "aria-invalid": hasSubmitted && !!errors.dob?.message,
                    },
                  }}
                />
                {dob && (
                  <Box mt={1}>
                    <Alert severity="info">{t("profile.form.age-alert", { age })}</Alert>
                  </Box>
                )}
              </Box>

              {requiresAnEmail && (
                <Box mb={2}>
                  <TextField
                    fullWidth
                    disabled
                    label={<FormLabel>{t("profile.form.sections.player.email")}</FormLabel>}
                    id="email"
                    type="email"
                    {...formMethods.register("email")}
                    helperText={(hasSubmitted && errors.email?.message) || ""}
                    error={hasSubmitted && !!errors.email?.message}
                    aria-invalid={hasSubmitted && !!errors.email?.message}
                  />
                </Box>
              )}

              {requiresAnEmail && (
                <Box mb={2}>
                  <TextField
                    fullWidth
                    label={<FormLabel>{t("profile.form.sections.player.phone")}</FormLabel>}
                    id="phone"
                    {...formMethods.register("phone")}
                    helperText={(hasSubmitted && errors.phone?.message) || ""}
                    error={hasSubmitted && !!errors.phone?.message}
                    aria-invalid={hasSubmitted && !!errors.phone?.message}
                  />
                </Box>
              )}

              <FormControl sx={{ mb: 2 }} fullWidth>
                <InputLabel htmlFor="gender-picker">
                  {t("profile.form.sections.player.gender.label")}
                </InputLabel>
                <Controller
                  name="gender.name"
                  control={formMethods.control}
                  render={(params) => (
                    <Select
                      fullWidth
                      required
                      id="gender-picker"
                      label={t("profile.form.sections.player.gender.label")}
                      inputProps={{ ...params.field }}
                    >
                      <MenuItem value={PlayerGenders.M}>
                        {t("profile.form.sections.player.gender.options.male")}
                      </MenuItem>
                      <MenuItem value={PlayerGenders.F}>
                        {t("profile.form.sections.player.gender.options.female")}
                      </MenuItem>
                      <MenuItem value={PlayerGenders.Undisclosed}>
                        {t("profile.form.sections.player.gender.options.undisclosed")}
                      </MenuItem>
                      <MenuItem value={PlayerGenders.Other}>
                        {t("profile.form.sections.player.gender.options.other")}
                      </MenuItem>
                    </Select>
                  )}
                />
              </FormControl>

              {data.gender?.name === PlayerGenders.Other && (
                <FormControl sx={{ mb: 2 }} fullWidth>
                  <Controller
                    name="gender.description"
                    control={formMethods.control}
                    shouldUnregister
                    render={({ field }) => (
                      <TextField
                        fullWidth
                        label={
                          <FormLabel>
                            {t("profile.form.sections.player.gender.options.specify")}
                          </FormLabel>
                        }
                        {...field}
                        value={field.value || ""}
                      />
                    )}
                  />

                  {!!errors.gender?.description && (
                    <FormHelperText error={true}>
                      {errors.gender?.description?.message}
                    </FormHelperText>
                  )}
                </FormControl>
              )}

              <Box mb={2}>
                <TextField
                  fullWidth
                  label={
                    <FormLabel>{t("profile.form.sections.player.medical-conditions")}</FormLabel>
                  }
                  id="medicalCondition"
                  {...formMethods.register("medicalConditions")}
                  helperText={(hasSubmitted && errors.medicalConditions?.message) || ""}
                  error={hasSubmitted && !!errors.medicalConditions?.message}
                  aria-invalid={hasSubmitted && !!errors.medicalConditions?.message}
                />
              </Box>

              <Box mb={2}>
                <FormLabel>{t("profile.form.sections.player.experience.label")}</FormLabel>
                <Select
                  fullWidth
                  required
                  labelId="experience-label"
                  id="playingExperience"
                  {...formMethods.register("playingExperience")}
                  value={playingExperience}
                >
                  <MenuItem value={PlayingExperience.Club}>
                    {t("profile.form.sections.player.experience.options.club")}
                  </MenuItem>
                  <MenuItem value={PlayingExperience.School}>
                    {t("profile.form.sections.player.experience.options.school")}
                  </MenuItem>
                  <MenuItem value={PlayingExperience.Recreational}>
                    {t("profile.form.sections.player.experience.options.recreational")}
                  </MenuItem>
                  <MenuItem value={PlayingExperience.None}>
                    {t("profile.form.sections.player.experience.options.none")}
                  </MenuItem>
                </Select>
              </Box>

              <Box mb={2}>
                <TextField
                  fullWidth
                  multiline
                  rows={3}
                  label={
                    <FormLabel>
                      {t("profile.form.sections.player.experience.description")}
                    </FormLabel>
                  }
                  id="playingExperienceDescription"
                  {...formMethods.register("playingExperienceDescription")}
                  helperText={(hasSubmitted && errors.playingExperienceDescription?.message) || ""}
                  error={hasSubmitted && !!errors.playingExperienceDescription?.message}
                  aria-invalid={hasSubmitted && !!errors.playingExperienceDescription?.message}
                />
              </Box>

              <Box mb={2}>
                <TextField
                  fullWidth
                  label={<FormLabel>{t("profile.form.sections.player.position")}</FormLabel>}
                  id="preferredPlayingPosition"
                  {...formMethods.register("preferredPlayingPosition")}
                  helperText={errors.preferredPlayingPosition?.message || ""}
                  error={!!errors.preferredPlayingPosition?.message}
                  aria-invalid={!!errors.preferredPlayingPosition?.message}
                />
              </Box>

              <Box mb={2}>
                <TextField
                  fullWidth
                  multiline
                  rows={3}
                  label={
                    <FormLabel>{t("profile.form.sections.player.address.first-line")}</FormLabel>
                  }
                  id="address.address"
                  {...formMethods.register("address.address")}
                  helperText={(hasSubmitted && errors.address?.address?.message) || ""}
                  error={hasSubmitted && !!errors.address?.address?.message}
                  aria-invalid={hasSubmitted && !!errors.address?.address?.message}
                />
              </Box>

              <Box mb={2}>
                <TextField
                  fullWidth
                  label={
                    <FormLabel>{t("profile.form.sections.player.address.postcode")}</FormLabel>
                  }
                  id="address.postcode"
                  {...formMethods.register("address.postcode")}
                  helperText={(hasSubmitted && errors.address?.postcode?.message) || ""}
                  error={hasSubmitted && !!errors.address?.postcode?.message}
                  aria-invalid={hasSubmitted && !!errors.address?.postcode?.message}
                />
              </Box>
            </Box>

            {!!playerProfile.guardian && (
              <Box
                component="fieldset"
                sx={{ borderTop: "none", borderLeft: "none", borderRight: "none" }}
                mt={4}
              >
                <Box component="legend" mb={3}>
                  <Typography variant="h4" component="h2" mb={1}>
                    {t("profile.form.sections.guardian.title")}
                  </Typography>
                  <Typography variant="body2">
                    {t("profile.form.sections.guardian.subtitle")}
                  </Typography>
                </Box>

                <Box mb={2}>
                  <TextField
                    fullWidth
                    label={<FormLabel>{t("profile.form.sections.guardian.first-name")}</FormLabel>}
                    id="guardian.firstName"
                    {...formMethods.register("guardian.firstName")}
                    helperText={(hasSubmitted && errors.guardian?.firstName?.message) || ""}
                    error={hasSubmitted && !!errors.guardian?.firstName?.message}
                    aria-invalid={hasSubmitted && !!errors.guardian?.firstName?.message}
                  />
                </Box>

                <Box mb={2}>
                  <TextField
                    fullWidth
                    label={<FormLabel>{t("profile.form.sections.guardian.last-name")}</FormLabel>}
                    id="guardian.lastName"
                    {...formMethods.register("guardian.lastName")}
                    helperText={(hasSubmitted && errors.guardian?.lastName?.message) || ""}
                    error={hasSubmitted && !!errors.guardian?.lastName?.message}
                    aria-invalid={hasSubmitted && !!errors.guardian?.lastName?.message}
                  />
                </Box>

                <Box mb={2}>
                  <DatePicker
                    label={<FormLabel>{t("profile.form.sections.guardian.dob")}</FormLabel>}
                    value={guardianDob}
                    openTo="year"
                    maxDate={CustomDate.subYears(CustomNumber.castToPositiveInteger(18))}
                    format="dd/MM/y"
                    onChange={(newValue) => {
                      formMethods.setValue("guardian.dob", CustomDate.pastOrEmpty(newValue), {
                        shouldDirty: true,
                        shouldTouch: true,
                        shouldValidate: true,
                      });
                    }}
                    slotProps={{
                      textField: {
                        helperText: (hasSubmitted && errors.guardian?.dob?.message) || "",
                        error: hasSubmitted && !!errors.guardian?.dob?.message,
                        "aria-invalid": hasSubmitted && !!errors.guardian?.dob?.message,
                      },
                    }}
                  />
                </Box>

                <Box mb={2}>
                  <TextField
                    fullWidth
                    disabled
                    label={<FormLabel>{t("profile.form.sections.guardian.email")}</FormLabel>}
                    id="guardian.email"
                    type="email"
                    {...formMethods.register("guardian.email")}
                    helperText={(hasSubmitted && errors.guardian?.email?.message) || ""}
                    error={hasSubmitted && !!errors.guardian?.email?.message}
                    aria-invalid={hasSubmitted && !!errors.guardian?.email?.message}
                  />
                </Box>

                <Box mb={2}>
                  <TextField
                    fullWidth
                    label={<FormLabel>{t("profile.form.sections.guardian.phone")}</FormLabel>}
                    id="guardian.phone"
                    {...formMethods.register("guardian.phone")}
                    helperText={(hasSubmitted && errors.guardian?.phone?.message) || ""}
                    error={hasSubmitted && !!errors.guardian?.phone?.message}
                    aria-invalid={hasSubmitted && !!errors.guardian?.phone?.message}
                  />
                </Box>
              </Box>
            )}

            <Box mt={4}>
              <Box sx={{ display: "flex", justifyContent: "center" }}>
                <Button sx={{ width: 1 / 2 }} type="submit" loading={loading} variant="contained">
                  {t("profile.form.submit")}
                </Button>
              </Box>

              {serverError && (
                <Box mt={2}>
                  <Alert severity="error">{t("profile.form.error")}</Alert>
                </Box>
              )}
            </Box>
          </form>
        </Card>
      </Box>
    </CenteredLayout>
  );
};
