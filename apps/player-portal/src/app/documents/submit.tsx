import { FC } from "react";
import { useTranslation } from "react-i18next";

import { CustomDate, Player } from "@mio/helpers";
import { Button, ToggleSnackbar, playersState, useConfirmation, Alert, DoneIcon } from "@mio/ui";

type Props = {
  player: Player;
};

export const PlayerDocumentsSubmit: FC<Props> = ({ player }) => {
  const { t } = useTranslation();
  const submitDocuments = playersState.useSubmitPlayerDocuments();
  const submittedOn = Player.getDocumentsSubmitDate(player);
  const { ConfirmationUI, open } = useConfirmation({
    title: t("documents.submit.confirmTitle"),
    content: t("documents.submit.confirmContent"),
    onConfirm: () => {
      submitDocuments.mutate(player.id);
    },
  });

  return (
    <>
      {player.documents?.submitted && (
        <Alert severity="info">
          {t("documents.submit.submitted-on", {
            date: submittedOn ? CustomDate.toDisplayDate(submittedOn) : "N/A",
          })}
        </Alert>
      )}

      {!player.documents?.submitted && (
        <>
          <Button
            loading={submitDocuments.isLoading}
            onClick={open}
            variant="contained"
            startIcon={<DoneIcon />}
          >
            {t("documents.submit.button")}
          </Button>

          {ConfirmationUI}
        </>
      )}

      <ToggleSnackbar open={submitDocuments.isSuccess}>
        <Alert severity="success">{t("documents.submit.success")}</Alert>
      </ToggleSnackbar>

      <ToggleSnackbar open={submitDocuments.isError}>
        <Alert severity="error">{t("documents.submit.error")}</Alert>
      </ToggleSnackbar>
    </>
  );
};
