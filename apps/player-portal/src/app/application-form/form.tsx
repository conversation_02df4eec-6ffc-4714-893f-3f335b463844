import { FC } from "react";
import { Controller } from "react-hook-form";
import {
  Box,
  FormControlLabel,
  MenuItem,
  Select,
  TextField,
  Typography,
  Card,
  FormLabel,
  DatePicker,
  FormControl,
  Alert,
  Checkbox,
  CenteredLayout,
  Button,
  FormHelperText,
  InputLabel,
} from "@mio/ui";
import {
  PlayingExperience,
  PlayerGenders,
  CustomDate,
  CustomNumber,
  APIError,
  PublicOrganization,
  ErrorMessages,
} from "@mio/helpers";

import { Header } from "./header";
import { DataShape } from "./types";
import { usePlayerEligibility } from "./usePlayerEligibility";
import { useApplicationForm } from "./useApplicationForm";
import { Terms } from "./terms";
import { useTranslation } from "react-i18next";

type Props = {
  onSubmit: (data: DataShape) => void;
  organization: PublicOrganization;
  loading?: boolean;
  serverError: APIError | null;
  success?: boolean;
};

export const ApplicationForm: FC<Props> = ({
  onSubmit,
  loading,
  serverError,
  success,
  organization,
}) => {
  const { t } = useTranslation();
  const {
    formMethods,
    acceptingApplications,
    data,
    dob,
    age,
    requiresAnEmail,
    playingExperience,
    guardianDob,
    requiresAGuardian,
    acceptedTerms,
    terms,
    hasTerms,
    errors,
  } = useApplicationForm(organization);

  const { EligibilityIndicator, isChecking } = usePlayerEligibility(data, organization.id);

  return (
    <CenteredLayout>
      <Box mb={3} component="header" p={2}>
        <Header organization={organization} />
      </Box>

      <Box component="main" p={2}>
        {success && <Alert severity="success">{organization.applications?.successMessage}</Alert>}

        {acceptingApplications && !success && (
          <Card sx={{ padding: 2 }}>
            <form onSubmit={formMethods.handleSubmit(onSubmit)}>
              <Box
                component="fieldset"
                sx={{ borderTop: "none", borderLeft: "none", borderRight: "none" }}
              >
                <Box component="legend" mb={3}>
                  <Typography variant="h4" component="h2" mb={1}>
                    {t("application.form.player.title")}
                  </Typography>
                  <Typography variant="body2">
                    {t("application.form.player.description")}
                  </Typography>
                </Box>

                <Box mb={2}>
                  <TextField
                    fullWidth
                    label={<FormLabel>{t("application.form.player.fields.first-name")}</FormLabel>}
                    id="firstName"
                    name="firstName"
                    inputProps={{ ...formMethods.register("firstName") }}
                    helperText={errors.firstName?.message || ""}
                    error={!!errors.firstName?.message}
                    aria-invalid={!!errors.firstName?.message}
                  />
                </Box>

                <Box mb={2}>
                  <TextField
                    fullWidth
                    label={<FormLabel>{t("application.form.player.fields.last-name")}</FormLabel>}
                    id="lastName"
                    name="lastName"
                    inputProps={{ ...formMethods.register("lastName") }}
                    helperText={errors.lastName?.message || ""}
                    error={!!errors.lastName?.message}
                    aria-invalid={!!errors.lastName?.message}
                  />
                </Box>

                <Box mb={3}>
                  <DatePicker
                    label={<FormLabel>{t("application.form.player.fields.dob")}</FormLabel>}
                    format="dd/MM/y"
                    openTo="year"
                    maxDate={new Date()}
                    value={dob}
                    onChange={(newValue) => {
                      formMethods.setValue("dob", CustomDate.pastOrEmpty(newValue), {
                        shouldDirty: true,
                        shouldTouch: true,
                        shouldValidate: true,
                      });
                    }}
                    slotProps={{
                      textField: {
                        helperText: errors.dob?.message || "",
                        error: !!errors.dob?.message,
                        "aria-invalid": !!errors.dob?.message,
                      },
                    }}
                  />
                  {dob && (
                    <Box mt={1}>
                      <Alert severity="info">
                        {t("application.form.player.fields.age-confirmation", { age })}
                      </Alert>
                    </Box>
                  )}
                </Box>

                {requiresAnEmail && (
                  <Box mb={2}>
                    <TextField
                      fullWidth
                      label={<FormLabel>{t("application.form.player.fields.email")}</FormLabel>}
                      id="email"
                      name="email"
                      type="email"
                      inputProps={{ ...formMethods.register("email") }}
                      helperText={errors.email?.message || ""}
                      error={!!errors.email?.message}
                      aria-invalid={!!errors.email?.message}
                    />
                    <Box mt={1}>
                      <EligibilityIndicator identificationType="email" />
                    </Box>
                  </Box>
                )}

                {requiresAnEmail && (
                  <Box mb={2}>
                    <TextField
                      fullWidth
                      label={<FormLabel>{t("application.form.player.fields.phone")}</FormLabel>}
                      id="phone"
                      name="phone"
                      inputProps={{ ...formMethods.register("phone") }}
                      helperText={errors.phone?.message || ""}
                      error={!!errors.phone?.message}
                      aria-invalid={!!errors.phone?.message}
                    />
                  </Box>
                )}

                <FormControl sx={{ mb: 2 }} fullWidth>
                  <InputLabel htmlFor="gender-picker">
                    {t("application.form.player.fields.gender")}
                  </InputLabel>
                  <Controller
                    name="gender.name"
                    control={formMethods.control}
                    render={(params) => (
                      <Select
                        fullWidth
                        required
                        id="gender-picker"
                        label={t("application.form.player.fields.gender")}
                        inputProps={{ ...params.field }}
                      >
                        <MenuItem value={PlayerGenders.M}>
                          {t("application.form.player.fields.gender-options.male")}
                        </MenuItem>
                        <MenuItem value={PlayerGenders.F}>
                          {t("application.form.player.fields.gender-options.female")}
                        </MenuItem>
                        <MenuItem value={PlayerGenders.Undisclosed}>
                          {t("application.form.player.fields.gender-options.undisclosed")}
                        </MenuItem>
                        <MenuItem value={PlayerGenders.Other}>
                          {t("application.form.player.fields.gender-options.other")}
                        </MenuItem>
                      </Select>
                    )}
                  />
                </FormControl>

                {data.gender?.name === PlayerGenders.Other && (
                  <FormControl sx={{ mb: 2 }} fullWidth>
                    <Controller
                      name="gender.description"
                      control={formMethods.control}
                      shouldUnregister
                      render={({ field }) => (
                        <TextField
                          fullWidth
                          label={
                            <FormLabel>
                              {t("application.form.player.fields.gender-other")}
                            </FormLabel>
                          }
                          {...field}
                          value={field.value || ""}
                        />
                      )}
                    />

                    {!!errors.gender?.description && (
                      <FormHelperText error={true}>
                        {errors.gender?.description?.message}
                      </FormHelperText>
                    )}
                  </FormControl>
                )}

                <Box mb={2}>
                  <TextField
                    fullWidth
                    label={
                      <FormLabel>
                        {t("application.form.player.fields.medical-conditions")}
                      </FormLabel>
                    }
                    id="medicalCondition"
                    name="medicalCondition"
                    inputProps={{ ...formMethods.register("medicalConditions") }}
                    helperText={errors.medicalConditions?.message || ""}
                    error={!!errors.medicalConditions?.message}
                    aria-invalid={!!errors.medicalConditions?.message}
                  />
                </Box>

                <Box mb={2}>
                  <FormLabel>{t("application.form.player.fields.playing-experience")}</FormLabel>
                  <Select
                    fullWidth
                    required
                    labelId="experience-label"
                    id="playingExperience"
                    inputProps={{ ...formMethods.register("playingExperience") }}
                    value={playingExperience}
                  >
                    <MenuItem value={PlayingExperience.Club}>
                      {t("application.form.player.fields.playing-experience-options.club")}
                    </MenuItem>
                    <MenuItem value={PlayingExperience.School}>
                      {t("application.form.player.fields.playing-experience-options.school")}
                    </MenuItem>
                    <MenuItem value={PlayingExperience.Recreational}>
                      {t("application.form.player.fields.playing-experience-options.recreational")}
                    </MenuItem>
                    <MenuItem value={PlayingExperience.None}>
                      {t("application.form.player.fields.playing-experience-options.none")}
                    </MenuItem>
                  </Select>
                </Box>

                <Box mb={2}>
                  <TextField
                    fullWidth
                    multiline
                    rows={3}
                    label={
                      <FormLabel>
                        {t("application.form.player.fields.experience-description")}
                      </FormLabel>
                    }
                    id="playingExperienceDescription"
                    name="playingExperienceDescription"
                    inputProps={{ ...formMethods.register("playingExperienceDescription") }}
                    helperText={errors.playingExperienceDescription?.message || ""}
                    error={!!errors.playingExperienceDescription?.message}
                    aria-invalid={!!errors.playingExperienceDescription?.message}
                  />
                </Box>

                <Box mb={2}>
                  <TextField
                    fullWidth
                    label={
                      <FormLabel>
                        {t("application.form.player.fields.preferred-playing-position")}
                      </FormLabel>
                    }
                    id="preferredPlayingPosition"
                    name="preferredPlayingPosition"
                    inputProps={{ ...formMethods.register("preferredPlayingPosition") }}
                    helperText={errors.preferredPlayingPosition?.message || ""}
                    error={!!errors.preferredPlayingPosition?.message}
                    aria-invalid={!!errors.preferredPlayingPosition?.message}
                  />
                </Box>

                <Box mb={2}>
                  <TextField
                    fullWidth
                    multiline
                    rows={3}
                    label={
                      <FormLabel>{t("application.form.player.fields.address.address")}</FormLabel>
                    }
                    id="address.address"
                    name="address.address"
                    inputProps={{ ...formMethods.register("address.address") }}
                    helperText={errors.address?.address?.message || ""}
                    error={!!errors.address?.address?.message}
                    aria-invalid={!!errors.address?.address?.message}
                  />
                </Box>

                <Box mb={2}>
                  <TextField
                    fullWidth
                    label={
                      <FormLabel>{t("application.form.player.fields.address.postcode")}</FormLabel>
                    }
                    id="address.postcode"
                    name="address.postcode"
                    inputProps={{ ...formMethods.register("address.postcode") }}
                    helperText={errors.address?.postcode?.message || ""}
                    error={!!errors.address?.postcode?.message}
                    aria-invalid={!!errors.address?.postcode?.message}
                  />
                </Box>
              </Box>

              {requiresAGuardian && (
                <Box
                  component="fieldset"
                  sx={{ borderTop: "none", borderLeft: "none", borderRight: "none" }}
                  mt={4}
                >
                  <Box component="legend" mb={3}>
                    <Typography variant="h4" component="h2" mb={1}>
                      {t("application.form.guardian.title")}
                    </Typography>
                    <Typography variant="body2">
                      {t("application.form.guardian.description")}
                    </Typography>
                  </Box>

                  <Box mb={2}>
                    <TextField
                      fullWidth
                      label={
                        <FormLabel>{t("application.form.guardian.fields.first-name")}</FormLabel>
                      }
                      id="guardian.firstName"
                      name="guardian.firstName"
                      inputProps={{ ...formMethods.register("guardian.firstName") }}
                      helperText={errors.guardian?.firstName?.message || ""}
                      error={!!errors.guardian?.firstName?.message}
                      aria-invalid={!!errors.guardian?.firstName?.message}
                    />
                  </Box>

                  <Box mb={2}>
                    <TextField
                      fullWidth
                      label={
                        <FormLabel>{t("application.form.guardian.fields.last-name")}</FormLabel>
                      }
                      id="guardian.lastName"
                      name="guardian.lastName"
                      inputProps={{ ...formMethods.register("guardian.lastName") }}
                      helperText={errors.guardian?.lastName?.message || ""}
                      error={!!errors.guardian?.lastName?.message}
                      aria-invalid={!!errors.guardian?.lastName?.message}
                    />
                  </Box>

                  <Box mb={2}>
                    <DatePicker
                      label={<FormLabel>{t("application.form.guardian.fields.dob")}</FormLabel>}
                      value={guardianDob}
                      openTo="year"
                      maxDate={CustomDate.subYears(CustomNumber.castToPositiveInteger(18))}
                      format="dd/MM/y"
                      onChange={(newValue) => {
                        formMethods.setValue("guardian.dob", CustomDate.pastOrEmpty(newValue), {
                          shouldDirty: true,
                          shouldTouch: true,
                          shouldValidate: true,
                        });
                      }}
                      slotProps={{
                        textField: {
                          helperText: errors.guardian?.dob?.message || "",
                          error: !!errors.guardian?.dob?.message,
                          "aria-invalid": !!errors.guardian?.dob?.message,
                        },
                      }}
                    />
                  </Box>

                  <Box mb={2}>
                    <TextField
                      fullWidth
                      label={<FormLabel>{t("application.form.guardian.fields.email")}</FormLabel>}
                      id="guardian.email"
                      name="guardian.email"
                      type="email"
                      inputProps={{ ...formMethods.register("guardian.email") }}
                      helperText={errors.guardian?.email?.message || ""}
                      error={!!errors.guardian?.email?.message}
                      aria-invalid={!!errors.guardian?.email?.message}
                    />
                    <Box mt={1}>
                      <EligibilityIndicator identificationType="guardian" />
                    </Box>
                  </Box>

                  <Box mb={2}>
                    <TextField
                      fullWidth
                      label={<FormLabel>{t("application.form.guardian.fields.phone")}</FormLabel>}
                      id="guardian.phone"
                      name="guardian.phone"
                      inputProps={{ ...formMethods.register("guardian.phone") }}
                      helperText={errors.guardian?.phone?.message || ""}
                      error={!!errors.guardian?.phone?.message}
                      aria-invalid={!!errors.guardian?.phone?.message}
                    />
                  </Box>
                </Box>
              )}

              <Box mt={4}>
                {hasTerms && (
                  <Box mb={2} ml={2}>
                    <FormControlLabel
                      label={t("application.form.terms.agree")}
                      control={
                        <Controller
                          name="acceptedTerms"
                          control={formMethods.control}
                          render={(props) => (
                            <Checkbox
                              value={props.field.value}
                              onChange={(e) => props.field.onChange(!!e.target.checked)}
                            />
                          )}
                        />
                      }
                    />
                    <Terms content={terms} />
                  </Box>
                )}

                <Box sx={{ display: "flex", justifyContent: "center" }}>
                  <Button
                    sx={{ width: 1 / 2 }}
                    type="submit"
                    loading={loading || isChecking}
                    disabled={!!hasTerms && !acceptedTerms}
                    variant="contained"
                  >
                    {hasTerms && !acceptedTerms
                      ? t("application.form.terms.accept-first")
                      : t("application.form.submit")}
                  </Button>
                </Box>

                {serverError && serverError.message !== ErrorMessages.EntityAlreadyExists && (
                  <Box mt={2}>
                    <Alert severity="error">{t("application.form.errors.generic")}</Alert>
                  </Box>
                )}

                {serverError && serverError.message === ErrorMessages.EntityAlreadyExists && (
                  <Box mt={2}>
                    <Alert severity="error">
                      {t("application.form.errors.already-registered")}
                    </Alert>
                  </Box>
                )}
              </Box>
            </form>
          </Card>
        )}
      </Box>
    </CenteredLayout>
  );
};
