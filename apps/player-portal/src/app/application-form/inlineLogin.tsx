import { FC, useState } from "react";
import { useTranslation } from "react-i18next";

import {
  Modal,
  Stack,
  TextField,
  Typography,
  Button,
  Box,
  playersAuthState,
  LocalError,
} from "@mio/ui";
import { Email, PlayerUser, isError } from "@mio/helpers";

type Props = {
  email: Email;
};

const InlineLoginDialog: FC<Props> = ({ email }) => {
  const { t } = useTranslation();
  const [code, setCode] = useState<string | undefined>(undefined);
  const { login, requestCode } = playersAuthState.useAuth();

  const isAtSecondStep = requestCode.isSuccess;

  const handleLogin = () => {
    const parsed = PlayerUser.Entity.toLoginCodeDto({ code });

    if (!isError(parsed)) {
      login.mutate(parsed, {
        onSuccess: () => {
          window.location.reload();
        },
      });
    }
  };

  return (
    <Stack gap={3} component="form" p={2}>
      <TextField
        label={t("login.fields.email")}
        name="email"
        aria-disabled={true}
        disabled={true}
        value={email}
        type="email"
      />

      <Box>
        <Button
          variant="outlined"
          onClick={() => requestCode.mutate({ email })}
          loading={requestCode.isLoading}
        >
          {t("login.buttons.send-code")}
        </Button>

        {requestCode.isError && <LocalError message={t("login.errors.generic")} />}
      </Box>

      {isAtSecondStep && (
        <>
          <TextField
            name="loginCode"
            label={t("login.fields.login-code")}
            value={code}
            type="password"
            onChange={(event) => setCode(event.target.value)}
          />
          <Box>
            <Button
              variant="outlined"
              onClick={handleLogin}
              aria-disabled={!code}
              loading={login.isLoading}
            >
              {t("login.buttons.login")}
            </Button>
          </Box>

          {login.isError && <LocalError message={t("login.errors.generic")} />}
        </>
      )}
    </Stack>
  );
};

export const useInlineLogin = () => {
  const { t } = useTranslation();
  const [targetEmail, setTargetEmail] = useState<string | undefined>(undefined);
  const parsedEmail = Email.parse(targetEmail);

  return {
    openInlineLogin: (email?: string) => setTargetEmail(email),
    InlineLoginDialog: (
      <Modal
        open={!!targetEmail}
        fullWidth
        onClose={() => setTargetEmail(undefined)}
        title={
          <Typography variant="h5" component="h2">
            {t("login.title")}
          </Typography>
        }
        content={!isError(parsedEmail) ? <InlineLoginDialog email={parsedEmail} /> : null}
      />
    ),
  };
};
