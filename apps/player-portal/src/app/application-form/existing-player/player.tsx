import { FC, useState } from "react";
import { useTranslation } from "react-i18next";

import { OrganizationId, Player } from "@mio/helpers";
import {
  Card,
  LocalError,
  LocalLoader,
  Stack,
  Typography,
  playersState,
  Alert,
  Box,
  playerUserState,
  Button,
  Confirmation,
} from "@mio/ui";

type Props = {
  player: Player;
  organization: OrganizationId;
};

const ExistingPlayer: FC<Props> = ({ player, organization }) => {
  const { t } = useTranslation();
  const [visibleConfirmation, toggleConfirmation] = useState(false);
  const myUser = playerUserState.useProvidedCurrentPlayerUser();
  const isAlreadyInOrganization = playersState.useFindOrganizationPlayer({
    organizationId: organization,
    playerId: player.id,
  });
  const applyToOrganization = playersState.useApplyToOrganization();

  if (isAlreadyInOrganization.isLoading) {
    return <LocalLoader />;
  }

  if (isAlreadyInOrganization.isError) {
    return <LocalError />;
  }

  const handleApply = () => {
    applyToOrganization.mutate(
      {
        organizationId: organization,
        playerId: player.id,
      },
      {
        onSuccess: () => {
          isAlreadyInOrganization.refetch();
        },
      },
    );
  };

  return (
    <Card>
      <Confirmation
        open={visibleConfirmation}
        onClose={() => toggleConfirmation(false)}
        onConfirm={() => {
          handleApply();
          toggleConfirmation(false);
        }}
        title={t("application.confirm-title")}
        content={t("application.confirm-content")}
      />

      <Stack gap={2} p={2}>
        <Typography fontWeight="bold">
          {Player.getFullName(player)}{" "}
          {player.email === myUser.authentication.email
            ? t("application.labels.me")
            : t("application.labels.guardian")}
        </Typography>

        {!isAlreadyInOrganization.data && (
          <Box>
            <Button
              variant="contained"
              onClick={() => toggleConfirmation(true)}
              loading={applyToOrganization.isLoading}
            >
              {applyToOrganization.isLoading
                ? t("application.apply-button.applying")
                : t("application.apply-button.apply")}
            </Button>
          </Box>
        )}

        {isAlreadyInOrganization.data && (
          <Alert severity="info">{t("application.already-applied")}</Alert>
        )}
      </Stack>
    </Card>
  );
};

export default ExistingPlayer;
