import { FC } from "react";
import { useForm } from "react-hook-form";
import { omitBy, isObject } from "lodash/fp";
import { useTranslation } from "react-i18next";

import {
  Box,
  Stack,
  InputLabel,
  Select,
  MenuItem,
  SearchIcon,
  Button,
  FormControl,
  playersState,
  useOrganization,
  useFormResolver,
} from "@mio/ui";
import { isError, Player, PlayerSorting, PlayerTeamStatus } from "@mio/helpers";
import { DataShape } from "./types";

type Props = {
  isLoading: boolean;
  onSubmit: (dto: playersState.PlayerQueryParams) => void;
};

const sortingDisplayNames = {
  [PlayerSorting.CreationDate]: "players.filters.sort-by.options.creation-date",
  [PlayerSorting.Names]: "players.filters.sort-by.options.names",
  [PlayerSorting.Age]: "players.filters.sort-by.options.age",
} as const;

export const PlayerFilters: FC<Props> = ({ onSubmit, isLoading }) => {
  const { t } = useTranslation();
  const organization = useOrganization();

  const formResolver = useFormResolver<DataShape>(Player.parseQueryDto);

  const { register, handleSubmit, watch } = useForm<DataShape>({
    defaultValues: {
      teamId: "",
      playerStatus: "",
      sortBy: PlayerSorting.CreationDate,
    },
    mode: "onChange",
    resolver: formResolver,
  });

  const query = watch();

  const handleFormSubmit = (data: unknown) => {
    const withoutEmptyFields = omitBy((value) => value === "", isObject(data) ? data : {});

    const parsed = Player.parseQueryDto(withoutEmptyFields);

    if (!isError(parsed)) {
      onSubmit({ organizationId: organization.id, query: parsed });
    }
  };

  return (
    <Box
      width={1}
      mt={1}
      component="form"
      onSubmit={handleSubmit(handleFormSubmit)}
      sx={{ width: "100%", bgcolor: "background.paper" }}
    >
      <Stack direction="row" alignItems="center" flexWrap="wrap" gap={2}>
        <FormControl sx={{ width: { xs: 1, md: 1 / 5 } }}>
          <InputLabel>{t("players.filters.status.label")}</InputLabel>
          <Select
            fullWidth
            labelId="player-status-label"
            id="playerStatus"
            label={t("players.filters.status.label")}
            name="playerStatus"
            inputProps={{ ...register("playerStatus") }}
            value={query.playerStatus}
          >
            <MenuItem value="" key="none">
              {t("players.filters.status.none")}
            </MenuItem>
            {Object.values(PlayerTeamStatus).map((status) => (
              <MenuItem value={status} key={status}>
                {t(`players.status.${status}`).toLowerCase()}
              </MenuItem>
            ))}
          </Select>
        </FormControl>
        <FormControl sx={{ width: { xs: 1, md: 1 / 5 } }}>
          <InputLabel id="sortBy-label">{t("players.filters.sort-by.label")}</InputLabel>
          <Select
            fullWidth
            labelId="sortBy-label"
            label={t("players.filters.sort-by.label")}
            name="sortBy"
            inputProps={{ ...register("sortBy") }}
            value={query.sortBy}
          >
            {Object.values(PlayerSorting).map((sortingOption) => (
              <MenuItem value={sortingOption} key={sortingOption}>
                {t(sortingDisplayNames[sortingOption])}
              </MenuItem>
            ))}
          </Select>
        </FormControl>
        <Stack justifyContent="center" alignItems="center">
          <Button
            type="submit"
            loading={isLoading}
            startIcon={<SearchIcon />}
            variant="contained"
            size="large"
            color="secondary"
          >
            {t("players.filters.search")}
          </Button>
        </Stack>
      </Stack>
    </Box>
  );
};
