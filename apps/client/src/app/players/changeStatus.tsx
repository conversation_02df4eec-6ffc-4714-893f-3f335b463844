import { FC, useState } from "react";
import { useTranslation } from "react-i18next";

import {
  Box,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Select,
  FormLabel,
  MenuItem,
  LocalError,
  useOrganization,
  playersState,
  Checkbox,
  Typography,
} from "@mio/ui";
import {
  AssignedPlayerTeamProfile,
  Player,
  PlayerTeamStatusWithTeam,
  PopulatedPlayer,
} from "@mio/helpers";

type Props = {
  player: PopulatedPlayer;
  playerProfile: AssignedPlayerTeamProfile;
  changeStatus: playersState.ChangeStatusHandler;
};

export const ChangeStatus: FC<Props> = ({ player, playerProfile, changeStatus }) => {
  const { t } = useTranslation();
  const [showDialog, toggleDialog] = useState(false);
  const [selectedStatus, setSelectedStatus] = useState<PlayerTeamStatusWithTeam>(
    playerProfile.status,
  );
  const [startRegistrationProcess, setStartRegistrationProcess] = useState(true);
  const organization = useOrganization();

  const statuses = Object.values(PlayerTeamStatusWithTeam);

  const closeDialog = () => {
    setSelectedStatus(playerProfile.status);
    toggleDialog(false);
  };

  const handleSubmit = () => {
    if (selectedStatus !== playerProfile.status) {
      changeStatus.mutate(
        {
          organizationId: organization.id,
          teamId: playerProfile.teamId,
          playerId: player.id,
          status: selectedStatus,
          startRegistrationProcess,
        },
        {
          onSuccess: closeDialog,
        },
      );
    }
  };

  return (
    <>
      <Box>
        <Button onClick={() => toggleDialog(true)} variant="contained" color="secondary">
          {t("players.status.change")}
        </Button>
      </Box>

      <Dialog open={showDialog}>
        <DialogTitle id="confirmation-dialog-title" sx={{ mb: 2 }}>
          {t("players.status.change-title", { name: Player.getFullName(player) })}
        </DialogTitle>

        <DialogContent>
          <Box mb={2}>
            <Box mb={2}>
              <FormLabel>{t("players.status.status-label")}</FormLabel>
            </Box>

            <Select<PlayerTeamStatusWithTeam>
              fullWidth
              onChange={(event) => {
                const newStatus = event.target.value as PlayerTeamStatusWithTeam;
                setSelectedStatus(newStatus);
              }}
              value={selectedStatus}
            >
              {statuses.map((status) => (
                <MenuItem key={status} value={status}>
                  {t(`players.status.${status}`)}
                </MenuItem>
              ))}
            </Select>

            {selectedStatus === PlayerTeamStatusWithTeam.RegistrationPending && (
              <Box sx={{ display: "flex", alignItems: "center" }}>
                <Checkbox
                  sx={{ paddingLeft: 0, paddingTop: 0 }}
                  checked={startRegistrationProcess}
                  onChange={(event: React.ChangeEvent<HTMLInputElement>) => {
                    setStartRegistrationProcess(event.target.checked);
                  }}
                />
                <Box mt={1} pt={1}>
                  <Typography>{t("players.status.registration.start-process")}</Typography>
                  <Typography>
                    {t("players.status.registration.start-process-description")}
                  </Typography>
                </Box>
              </Box>
            )}

            {changeStatus.isError && (
              <Box mt={2}>
                <LocalError message={t("players.status.error")} />
              </Box>
            )}
          </Box>
        </DialogContent>
        <DialogActions>
          <Button variant="outlined" onClick={closeDialog} sx={{ mr: 3 }}>
            {t("players.status.cancel")}
          </Button>
          <Button
            variant="contained"
            onClick={handleSubmit}
            disabled={selectedStatus === playerProfile.status}
            loading={changeStatus.isLoading}
          >
            {t("players.status.save")}
          </Button>
        </DialogActions>
      </Dialog>
    </>
  );
};
