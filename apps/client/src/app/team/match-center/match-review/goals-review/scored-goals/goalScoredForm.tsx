import { FC } from "react";
import { useForm } from "react-hook-form";
import { useTranslation } from "react-i18next";

import {
  Box,
  TextField,
  Stack,
  LocalError,
  Button,
  useFormResolver,
  useRequiredTeamId,
  InputLabel,
  Select,
  MenuItem,
  footballMatchState,
  ToggleSnackbar,
  Alert,
  useOrganization,
  Typography,
  useTheme,
  FormControl,
  Grid,
} from "@mio/ui";
import { APIError, FootballMatchAttemptFor, PopulatedPlayer, Primitive } from "@mio/helpers";

type DataShape = Partial<Primitive<FootballMatchAttemptFor.CreateDto> & { id?: string }>;
type Props = {
  attemptFor?: FootballMatchAttemptFor.AttemptFor;
  onSubmit: (data: DataShape) => void;
  loading?: boolean;
  serverError?: APIError | null;
  upsertReviewIsSuccess: boolean;
  teamPlayers: PopulatedPlayer[];
  index: number;
};

export const AttemptForForm: FC<Props> = ({
  attemptFor,
  onSubmit,
  loading,
  serverError,
  upsertReviewIsSuccess,
  teamPlayers,
  index,
}) => {
  const { t } = useTranslation();
  const organization = useOrganization();
  const teamId = useRequiredTeamId();
  const footballMatchPlanId = footballMatchState.useRequiredFootballMatchPlanId();
  const formResolver = useFormResolver(FootballMatchAttemptFor.Entity.toUpsertDto);
  const {
    FootballMatchHalves,
    AttemptForBodyPart,
    GoalSituationOpenPlaySubType,
    GoalSituationSetPieceSubType,
    GoalSituationType,
    AttemptForType,
    GoalType,
  } = FootballMatchAttemptFor;
  const { register, handleSubmit, formState, watch } = useForm<DataShape>({
    defaultValues: {
      footballMatchPlanId: footballMatchPlanId,
      organizationId: organization.id,
      teamId: teamId,
      id: attemptFor?.id,
      addedTime: attemptFor?.addedTime,
      assist: attemptFor?.assist,
      assistBodyPart: attemptFor?.assistBodyPart,
      assistZone: attemptFor?.assistZone,
      bodyPart: attemptFor?.bodyPart,
      type: attemptFor?.type || AttemptForType.goal,
      half: attemptFor?.half,
      keyPass: attemptFor?.keyPass,
      keyPassZone: attemptFor?.keyPassZone,
      minute: attemptFor?.minute,
      shooter: attemptFor?.shooter,
      zone: attemptFor?.zone,
      keyContributions: attemptFor?.keyContributions,
      highlights: attemptFor?.highlights,
      situationType: attemptFor?.situationType,
      situationOpenPlaySubType: attemptFor?.situationOpenPlaySubType,
      situationSetPieceSubType: attemptFor?.situationSetPieceSubType,
    },
    resolver: formResolver,
  });
  const half = watch("half");
  const situationType = watch("situationType");
  const bodyPart = watch("bodyPart");
  const assist = watch("assist");
  const assistBodyPart = watch("assistBodyPart");
  const keyPass = watch("keyPass");
  const scorer = watch("shooter");
  const situationOpenPlaySubType = watch("situationOpenPlaySubType");
  const situationSetPieceSubType = watch("situationSetPieceSubType");
  const type = watch("type");
  const { errors, submitCount, isDirty } = formState;
  const hasSubmitted = submitCount > 0;
  return (
    <Box
      width={1}
      component="form"
      onSubmit={handleSubmit(onSubmit)}
      sx={{ width: "100%", bgcolor: "background.paper" }}
    >
      {/* Time Details Group */}
      <Typography variant="subtitle2" color="text.secondary" mt={2} mb={1}>
        {t("match.time-details")}
      </Typography>
      <Grid container spacing={2} mb={3}>
        {(!attemptFor || (attemptFor && attemptFor.type !== AttemptForType.goal)) && (
          <Grid columns={{ xs: 12, sm: 6, md: 4 }}>
            <FormControl fullWidth>
              <InputLabel
                id="attempt-type-label"
                sx={{
                  backgroundColor: "background.paper",
                  px: 1,
                }}
              >
                {t("match.attempt-type")}
              </InputLabel>
              <Select
                labelId="attempt-type-label"
                label={t("match.attempt-type")}
                fullWidth
                {...register(`type`)}
                value={type || AttemptForType.shotOffGoal}
              >
                <MenuItem value={AttemptForType.shotOffGoal}>
                  {t("match.attempt-type-options.shot-off-goal")}
                </MenuItem>
                <MenuItem value={AttemptForType.shotOnGoal}>
                  {t("match.attempt-type-options.shot-on-goal")}
                </MenuItem>
                <MenuItem value={AttemptForType.shotBlocked}>
                  {t("match.attempt-type-options.shot-blocked")}
                </MenuItem>
              </Select>
            </FormControl>
          </Grid>
        )}

        <Grid columns={{ xs: 12, sm: 6, md: 4 }}>
          <FormControl fullWidth>
            <TextField
              fullWidth
              label={t("match.minute")}
              error={hasSubmitted && !!errors?.minute?.message}
              helperText={hasSubmitted && errors.minute?.message}
              {...register(`minute`)}
            />
          </FormControl>
        </Grid>
        <Grid columns={{ xs: 12, sm: 6, md: 4 }}>
          <FormControl fullWidth>
            <TextField
              fullWidth
              label={t("match.added-time")}
              error={hasSubmitted && !!errors.addedTime?.message}
              helperText={hasSubmitted && errors.addedTime?.message}
              {...register(`addedTime`)}
            />
          </FormControl>
        </Grid>
        <Grid columns={{ xs: 12, sm: 6, md: 4 }}>
          <FormControl fullWidth>
            <InputLabel
              id="half-label"
              sx={{
                backgroundColor: "background.paper",
                px: 1,
              }}
            >
              {t("match.half")}
            </InputLabel>
            <Select
              labelId="half-label"
              label={t("match.half")}
              fullWidth
              {...register(`half`)}
              value={half}
            >
              <MenuItem value={FootballMatchHalves.first}>{t("match.halves.first")}</MenuItem>
              <MenuItem value={FootballMatchHalves.second}>{t("match.halves.second")}</MenuItem>
              <MenuItem value={FootballMatchHalves.extraTimeFirst}>
                {t("match.halves.extra-time-first")}
              </MenuItem>
              <MenuItem value={FootballMatchHalves.extraTimeSecond}>
                {t("match.halves.extra-time-second")}
              </MenuItem>
              <MenuItem value={FootballMatchHalves.afterMatchPenalties}>
                {t("match.halves.penalties")}
              </MenuItem>
            </Select>
          </FormControl>
        </Grid>
      </Grid>

      {/* Situation Group */}
      <Typography variant="subtitle2" color="text.secondary" mt={2} mb={1}>
        {t("match.situation-details")}
      </Typography>
      <Grid container spacing={2} mb={3}>
        <Grid columns={{ xs: 12, sm: 6 }}>
          <FormControl fullWidth>
            <InputLabel
              id="situation-type-label"
              sx={{
                backgroundColor: "background.paper",
                px: 1,
              }}
            >
              {t("match.situation")}
            </InputLabel>
            <Select
              labelId="situation-type-label"
              label={t("match.situation")}
              fullWidth
              {...register(`situationType`)}
              value={situationType}
            >
              <MenuItem value={GoalSituationType.openPlay}>
                {t("match.situation-detailed-options.open-play")}
              </MenuItem>
              <MenuItem value={GoalSituationType.setPiece}>
                {t("match.situation-detailed-options.set-piece")}
              </MenuItem>
              <MenuItem value={GoalSituationType.errorLeadingToAGoal}>
                {t("match.situation-detailed-options.error-leading-to-a-goal")}
              </MenuItem>
              <MenuItem value={GoalSituationType.pressing}>
                {t("match.situation-detailed-options.pressing")}
              </MenuItem>
            </Select>
          </FormControl>
        </Grid>
        {situationType && (
          <Grid columns={{ xs: 12, sm: 6 }}>
            <FormControl fullWidth>
              <InputLabel
                id="situation-subtype-label"
                sx={{
                  backgroundColor: "background.paper",
                  px: 1,
                }}
              >
                {t("match.situation-detailed")}
              </InputLabel>
              {situationType === GoalSituationType.openPlay ? (
                <Select
                  labelId="situation-subtype-label"
                  label={t("match.situation-detailed")}
                  fullWidth
                  {...register(`situationOpenPlaySubType`)}
                  value={situationOpenPlaySubType}
                >
                  <MenuItem value={GoalSituationOpenPlaySubType.combinationPlay}>
                    {t("match.open-play-sub-type.combination-play")}
                  </MenuItem>
                  <MenuItem value={GoalSituationOpenPlaySubType.counterAttack}>
                    {t("match.open-play-sub-type.counter-attack")}
                  </MenuItem>
                  <MenuItem value={GoalSituationOpenPlaySubType.cross}>
                    {t("match.open-play-sub-type.cross")}
                  </MenuItem>
                  <MenuItem value={GoalSituationOpenPlaySubType.dribble}>
                    {t("match.open-play-sub-type.dribble")}
                  </MenuItem>
                  <MenuItem value={GoalSituationOpenPlaySubType.longShot}>
                    {t("match.open-play-sub-type.long-shot")}
                  </MenuItem>
                  <MenuItem value={GoalSituationOpenPlaySubType.oneToOne}>
                    {t("match.open-play-sub-type.one-to-one")}
                  </MenuItem>
                  <MenuItem value={GoalSituationOpenPlaySubType.rebound}>
                    {t("match.open-play-sub-type.rebound")}
                  </MenuItem>
                  <MenuItem value={GoalSituationOpenPlaySubType.throughBall}>
                    {t("match.open-play-sub-type.through-ball")}
                  </MenuItem>
                </Select>
              ) : (
                <Select
                  labelId="situation-subtype-label"
                  label={t("match.situation-detailed")}
                  fullWidth
                  {...register(`situationSetPieceSubType`)}
                  value={situationSetPieceSubType}
                >
                  <MenuItem value={GoalSituationSetPieceSubType.corner}>
                    {t("match.set-piece-sub-type.corner")}
                  </MenuItem>
                  <MenuItem value={GoalSituationSetPieceSubType.directFreeKick}>
                    {t("match.set-piece-sub-type.direct-free-kick")}
                  </MenuItem>
                  <MenuItem value={GoalSituationSetPieceSubType.goalKick}>
                    {t("match.set-piece-sub-type.goal-kick")}
                  </MenuItem>
                  <MenuItem value={GoalSituationSetPieceSubType.indirectFreeKick}>
                    {t("match.set-piece-sub-type.indirect-free-kick")}
                  </MenuItem>
                  <MenuItem value={GoalSituationSetPieceSubType.penalty}>
                    {t("match.set-piece-sub-type.penalty")}
                  </MenuItem>
                  <MenuItem value={GoalSituationSetPieceSubType.throwIn}>
                    {t("match.set-piece-sub-type.throw-in")}
                  </MenuItem>
                </Select>
              )}
            </FormControl>
          </Grid>
        )}
      </Grid>

      {/* Goal Details Group */}
      <Typography variant="subtitle2" color="text.secondary" mt={2} mb={1}>
        {t("match.attempt-details")}
      </Typography>
      <Grid container spacing={2} mb={3}>
        {attemptFor && attemptFor.type === AttemptForType.goal && (
          <Grid columns={{ xs: 12, sm: 6, md: 4 }}>
            <FormControl fullWidth>
              <InputLabel
                id="goal-type-label"
                sx={{
                  backgroundColor: "background.paper",
                  px: 1,
                }}
              >
                {t("match.goal-type")}
              </InputLabel>
              <Select
                labelId="goal-type-label"
                label={t("match.goal-type")}
                fullWidth
                {...register(`type`)}
                value={type || AttemptForType.goal}
              >
                <MenuItem value={GoalType.goal}>{t("match.goal-type-normal")}</MenuItem>
                <MenuItem value={GoalType.ownGoal}>{t("match.goal-type-own")}</MenuItem>
              </Select>
            </FormControl>
          </Grid>
        )}

        <Grid columns={{ xs: 12, sm: 6, md: 4 }}>
          <FormControl fullWidth>
            <InputLabel
              id="scorer-label"
              sx={{
                backgroundColor: "background.paper",
                px: 1,
              }}
            >
              {t("match.shooter")}
            </InputLabel>
            <Select
              labelId="scorer-label"
              label={t("match.shooter")}
              fullWidth
              {...register(`shooter`)}
              value={scorer}
              disabled={
                attemptFor &&
                attemptFor.type === AttemptForType.goal &&
                attemptFor.goalType !== GoalType.goal
              }
            >
              {teamPlayers.map((player) => (
                <MenuItem key={player.profiles[0].id} value={player.profiles[0].id}>
                  {player.firstName} {player.lastName}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        </Grid>
        <Grid columns={{ xs: 12, sm: 6, md: 2 }}>
          <FormControl fullWidth>
            <InputLabel
              id="body-part-label"
              sx={{
                backgroundColor: "background.paper",
                px: 1,
              }}
            >
              {t("match.body-part")}
            </InputLabel>
            <Select
              labelId="body-part-label"
              label={t("match.body-part")}
              fullWidth
              {...register(`bodyPart`)}
              value={bodyPart}
            >
              <MenuItem value={AttemptForBodyPart.rightFoot}>
                {t("match.body-parts.right-foot")}
              </MenuItem>
              <MenuItem value={AttemptForBodyPart.leftFoot}>
                {t("match.body-parts.left-foot")}
              </MenuItem>
              <MenuItem value={AttemptForBodyPart.head}>{t("match.body-parts.head")}</MenuItem>
              <MenuItem value={AttemptForBodyPart.chest}>{t("match.body-parts.chest")}</MenuItem>
              <MenuItem value={AttemptForBodyPart.knee}>{t("match.body-parts.knee")}</MenuItem>
              <MenuItem value={AttemptForBodyPart.shoulder}>
                {t("match.body-parts.shoulder")}
              </MenuItem>
              <MenuItem value={AttemptForBodyPart.hip}>{t("match.body-parts.hip")}</MenuItem>
              <MenuItem value={AttemptForBodyPart.back}>{t("match.body-parts.back")}</MenuItem>
              <MenuItem value={AttemptForBodyPart.hand}>{t("match.body-parts.hand")}</MenuItem>
              <MenuItem value={AttemptForBodyPart.other}>{t("match.body-parts.other")}</MenuItem>
            </Select>
          </FormControl>
        </Grid>
      </Grid>

      {/* Assist Details Group */}
      <Typography variant="subtitle2" color="text.secondary" mt={2} mb={1}>
        {t("match.assist-details")}
      </Typography>
      <Grid container spacing={2} mb={3}>
        <Grid columns={{ xs: 12, sm: 6, md: 4 }}>
          <FormControl fullWidth>
            <InputLabel
              id="assist-label"
              sx={{
                backgroundColor: "background.paper",
                px: 1,
              }}
            >
              {t("match.assist")}
            </InputLabel>
            <Select
              labelId="assist-label"
              label={t("match.assist")}
              fullWidth
              {...register(`assist`)}
              value={assist}
            >
              {teamPlayers.map((player) => (
                <MenuItem key={player.profiles[0].id} value={player.profiles[0].id}>
                  {player.firstName} {player.lastName}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        </Grid>
        <Grid columns={{ xs: 12, sm: 6, md: 4 }}>
          <FormControl fullWidth>
            <InputLabel
              id="assist-body-part-label"
              sx={{
                backgroundColor: "background.paper",
                px: 1,
              }}
            >
              {t("match.assist-body-part")}
            </InputLabel>
            <Select
              labelId="assist-body-part-label"
              label={t("match.assist-body-part")}
              fullWidth
              {...register(`assistBodyPart`)}
              value={assistBodyPart}
            >
              <MenuItem value={AttemptForBodyPart.rightFoot}>
                {t("match.body-parts.right-foot")}
              </MenuItem>
              <MenuItem value={AttemptForBodyPart.leftFoot}>
                {t("match.body-parts.left-foot")}
              </MenuItem>
              <MenuItem value={AttemptForBodyPart.head}>{t("match.body-parts.head")}</MenuItem>
              <MenuItem value={AttemptForBodyPart.chest}>{t("match.body-parts.chest")}</MenuItem>
              <MenuItem value={AttemptForBodyPart.knee}>{t("match.body-parts.knee")}</MenuItem>
              <MenuItem value={AttemptForBodyPart.shoulder}>
                {t("match.body-parts.shoulder")}
              </MenuItem>
              <MenuItem value={AttemptForBodyPart.hip}>{t("match.body-parts.hip")}</MenuItem>
              <MenuItem value={AttemptForBodyPart.back}>{t("match.body-parts.back")}</MenuItem>
              <MenuItem value={AttemptForBodyPart.hand}>{t("match.body-parts.hand")}</MenuItem>
              <MenuItem value={AttemptForBodyPart.other}>{t("match.body-parts.other")}</MenuItem>
            </Select>
          </FormControl>
        </Grid>
      </Grid>

      {/* Key Pass Details Group */}
      <Typography variant="subtitle2" color="text.secondary" mt={2} mb={1}>
        {t("match.key-pass-details")}
      </Typography>
      <Grid container spacing={2} mb={3}>
        <Grid columns={{ xs: 12, sm: 6 }}>
          <FormControl fullWidth>
            <InputLabel
              id="key-pass-label"
              sx={{
                backgroundColor: "background.paper",
                px: 1,
              }}
            >
              {t("match.key-pass")}
            </InputLabel>
            <Select
              labelId="key-pass-label"
              label={t("match.key-pass")}
              fullWidth
              {...register(`keyPass`)}
              value={keyPass}
            >
              {teamPlayers.map((player) => (
                <MenuItem key={player.profiles[0].id} value={player.profiles[0].id}>
                  {player.firstName} {player.lastName}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        </Grid>
        <Grid columns={{ xs: 12, sm: 6 }}>
          <FormControl fullWidth>
            <TextField
              fullWidth
              label={t("match.key-pass-zone")}
              error={hasSubmitted && !!errors.keyPassZone?.message}
              helperText={hasSubmitted && errors.keyPassZone?.message}
              {...register(`keyPassZone`)}
            />
          </FormControl>
        </Grid>
      </Grid>

      {/* Submit Button */}
      <Box sx={{ display: "flex", justifyContent: "center", mt: 2 }}>
        <Button
          size="large"
          type="submit"
          loading={loading}
          variant="contained"
          color="secondary"
          disabled={!isDirty}
        >
          {t("match.save")}
        </Button>
      </Box>

      {serverError && <LocalError message="Something went wrong" />}
      <ToggleSnackbar open={upsertReviewIsSuccess}>
        <Alert severity="success">
          {t("match.review")} {`${attemptFor?.id ? t("match.updated") : t("match.created")}`}
        </Alert>
      </ToggleSnackbar>
    </Box>
  );
};
