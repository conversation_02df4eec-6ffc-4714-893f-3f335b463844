import { FC } from "react";
import { FormProvider, useFieldArray, useForm } from "react-hook-form";
import { useTranslation } from "react-i18next";

import {
  Box,
  Typography,
  Stack,
  LocalError,
  Button,
  useOrganizationId,
  useRequiredTeamId,
  footballMatchState,
  useTheme,
  useFormResolverNested,
  ToggleSnackbar,
  Alert,
  ErrorFormatter,
} from "@mio/ui";

import { APIError, FootballMatchPlayerReview, PopulatedPlayer, Primitive } from "@mio/helpers";

import { BackButton } from "../../../../shared";
import { PlayerRatingField } from "./playerRatingField";
import { DataShape } from "./types";
import { Dictionary } from "lodash";

type Props = {
  teamPlayers: PopulatedPlayer[];
  onSubmit: (data: DataShape) => void;
  loading?: boolean;
  serverError?: APIError | null;
  playersRatings?: FootballMatchPlayerReview.CompletePlayerReview[];
  createReviewIsSuccess: boolean;
};

// Filter out empty reviews
const filter = (value: Dictionary<unknown>) => {
  if (Array.isArray(value.reviews)) {
    value.reviews = value.reviews.filter(
      ({ overallRating, overallHighlights, overallMinutesPlayed, overallPosition }) =>
        !!overallRating || !!overallHighlights || !!overallMinutesPlayed || !!overallPosition,
    );
    return value;
  }
  return value;
};

export const PlayersRatingsForm: FC<Props> = ({
  playersRatings,
  teamPlayers,
  onSubmit,
  loading,
  serverError,
  createReviewIsSuccess,
}) => {
  const { t } = useTranslation();
  const organizationId = useOrganizationId();
  const teamId = useRequiredTeamId();
  const footballMatchPlanId = footballMatchState.useRequiredFootballMatchPlanId();
  const formResolver = useFormResolverNested(
    FootballMatchPlayerReview.Entity.toUpsertManyCustomDto,
    ErrorFormatter,
    filter,
  );

  const theme = useTheme();

  const formMethods = useForm<DataShape>({
    defaultValues: {
      reviews: teamPlayers.map((player) => {
        const playerRating = playersRatings?.find(
          (pr) => pr.playerTeamProfileId === player.profiles[0].id,
        );

        return {
          attendance: playerRating?.attendance,
          overallHighlights: playerRating?.overallHighlights,
          overallRating: playerRating?.overallRating,
          overallMinutesPlayed: playerRating?.overallMinutesPlayed,
          overallPosition: playerRating?.overallPosition,
          positionsPlayed: playerRating?.positionsPlayed,

          footballMatchReviewId: playerRating?.footballMatchReviewId,
          footballMatchPlanId: playerRating?.footballMatchPlanId || footballMatchPlanId,
          playerTeamProfileId: player?.profiles[0]?.id,
          organizationId: organizationId,
          teamId: teamId,
        };
      }),
    },
    resolver: formResolver as any,
  });

  const { watch, setValue, handleSubmit, formState, control } = formMethods;
  const { fields } = useFieldArray({
    control,
    name: "reviews",
  });

  const reviews = watch("reviews");

  const { errors, submitCount, dirtyFields } = formState;
  const hasSubmitted = submitCount > 0;

  return (
    <FormProvider {...formMethods}>
      <Box
        width={1}
        component="form"
        onSubmit={handleSubmit(onSubmit)}
        sx={{ width: "100%", bgcolor: "background.paper" }}
      >
        <Stack direction="row" alignItems="center" flexWrap="wrap">
          <Box>
            <BackButton path="../">{t("matches.review.players.back-to-match-center")}</BackButton>
          </Box>
          <Box component="legend" display="block" sx={{ flexGrow: 1, pr: 5 }}>
            <Typography variant="h5" component="h1" textAlign="center">
              {t("matches.review.players.title")}
            </Typography>
          </Box>
        </Stack>

        {fields.map((field, index) => (
          <PlayerRatingField
            key={field.id}
            index={index}
            player={teamPlayers[index]}
            review={reviews[index]}
            setValue={setValue}
            onSubmit={handleSubmit(onSubmit)}
            control={control}
            errors={errors}
            dirtyFields={dirtyFields}
            hasSubmitted={hasSubmitted}
            theme={theme}
          />
        ))}

        <Box mt={5} mb={2} sx={{ display: "flex", justifyContent: "center" }}>
          <Button
            size="large"
            type="submit"
            loading={loading}
            variant="contained"
            color="secondary"
          >
            {t("matches.review.players.save")}
          </Button>
        </Box>

        {serverError && <LocalError message={t("matches.review.players.error")} />}

        <ToggleSnackbar open={createReviewIsSuccess}>
          <Alert severity="success">{t("matches.review.players.success")}</Alert>
        </ToggleSnackbar>
      </Box>
    </FormProvider>
  );
};
