import { FC } from "react";
import { Control, Controller, FieldErrors, UseFormSetValue } from "react-hook-form";
import { NumericFormat } from "react-number-format";
import { useTranslation } from "react-i18next";

import {
  Box,
  Typography,
  Stack,
  IconButton,
  Divider,
  TextField,
  RemoveIcon,
  AddIcon,
  Theme,
  Tooltip,
  CommentIcon,
  useMediaQuery,
} from "@mio/ui";
import { PopulatedPlayer, Player, FootballMatchPlayerReview, Primitive } from "@mio/helpers";
import { DataShape } from "./types";
import { useEditPlayerReview } from "./additional/useEditPlayerReview";

type PlayerRatingFieldProps = {
  index: number;
  player: PopulatedPlayer;
  review?: Partial<Primitive<FootballMatchPlayerReview.CreateDto> & { id?: string }>;
  setValue: UseFormSetValue<DataShape>;
  onSubmit: () => void;
  control: Control<DataShape, any>;
  errors: FieldErrors<DataShape>;
  dirtyFields: any;
  hasSubmitted: boolean;
  theme: Theme;
  min?: number;
  max?: number;
  step?: number;
  defaultValue?: number;
};

export const PlayerRatingField: FC<PlayerRatingFieldProps> = ({
  index,
  player,
  review,
  setValue,
  onSubmit,
  control,
  errors,
  dirtyFields,
  hasSubmitted,
  theme,
  min = 1,
  max = 10,
  step = 0.5,
  defaultValue = 6.5,
}) => {
  const { t } = useTranslation();
  const isMobile = useMediaQuery((theme: Theme) => theme.breakpoints.down("sm"));
  const playerName = Player.getFullName(player);

  const hasReviewDetails = review ? FootballMatchPlayerReview.Entity.reviewExists(review) : false;

  const hasReviewDetailError =
    !!errors.reviews?.[index]?.overallHighlights ||
    !!errors.reviews?.[index]?.overallMinutesPlayed ||
    !!errors.reviews?.[index]?.overallPosition ||
    !!errors.reviews?.[index]?.attendance;

  const { EditPlayerReviewUI, editPlayerReview } = useEditPlayerReview({
    playerName,
    onSubmit,
  });

  return (
    <Stack key={playerName} gap={2}>
      <Stack
        direction={isMobile ? "column" : "row"}
        spacing={1}
        mt={1}
        alignItems={isMobile ? "center" : "center"}
        justifyContent={isMobile ? "center" : "space-between"}
      >
        <Typography variant="h6" component="h2">
          {playerName}
        </Typography>

        <Stack
          alignItems="center"
          direction="row"
          justifyContent="center"
          gap={1}
          mb={isMobile ? 1 : 0}
          width={isMobile ? "100%" : "auto"}
          position="relative"
        >
          <Stack direction="row" gap={1} justifyContent="center">
            <Tooltip title={t("matches.review.players.review.rating.decrease")}>
              <IconButton
                aria-label={t("matches.review.players.review.rating.decrease")}
                size="small"
                sx={{ borderRadius: "100%", backgroundColor: theme.palette.other.lightGrey }}
                onClick={() => {
                  if (review?.overallRating === min) return;

                  setValue(
                    `reviews.${index}.overallRating`,
                    (review?.overallRating || defaultValue) - step,
                    {
                      shouldValidate: true,
                      shouldDirty: true,
                      shouldTouch: true,
                    },
                  );
                }}
              >
                <RemoveIcon />
              </IconButton>
            </Tooltip>
            <Box width={2 / 5}>
              <Controller
                name={`reviews.${index}.overallRating`}
                control={control}
                render={({ field }) => (
                  <NumericFormat
                    {...field}
                    customInput={TextField}
                    size="small"
                    fixedDecimalScale
                    decimalScale={1}
                    decimalSeparator="."
                    placeholder={defaultValue.toString()}
                    helperText={
                      hasSubmitted &&
                      dirtyFields.reviews?.[index] &&
                      errors.reviews?.[index]?.overallRating?.message
                    }
                    error={
                      hasSubmitted &&
                      dirtyFields.reviews?.[index] &&
                      !!errors.reviews?.[index]?.overallRating?.message
                    }
                    aria-invalid={
                      hasSubmitted &&
                      dirtyFields.reviews?.[index] &&
                      !!errors.reviews?.[index]?.overallRating?.message
                    }
                    sx={{
                      textAlign: "center",
                      "& input": { textAlign: "center", paddingLeft: "2px", paddingRight: "2px" },
                    }}
                    onValueChange={(values) => {
                      setValue(`reviews.${index}.overallRating`, values.floatValue, {
                        shouldValidate: true,
                        shouldDirty: true,
                        shouldTouch: true,
                      });
                    }}
                  />
                )}
              />
            </Box>
            <Tooltip title={t("matches.review.players.review.rating.increase")}>
              <IconButton
                aria-label={t("matches.review.players.review.rating.increase")}
                size="small"
                sx={{ borderRadius: "100%", backgroundColor: theme.palette.other.lightGrey }}
                onClick={() => {
                  if (review?.overallRating === max) return;

                  setValue(
                    `reviews.${index}.overallRating`,
                    (review?.overallRating || defaultValue) + step,
                    {
                      shouldValidate: true,
                      shouldDirty: true,
                      shouldTouch: true,
                    },
                  );
                }}
              >
                <AddIcon />
              </IconButton>
            </Tooltip>
          </Stack>

          <Box sx={{ position: isMobile ? "absolute" : "relative", right: 0 }}>
            <Tooltip
              title={t(
                `matches.review.players.review.${hasReviewDetails ? "editReview" : "addReview"}`,
              )}
            >
              <IconButton
                aria-label={t(
                  `matches.review.players.review.${hasReviewDetails ? "editReview" : "addReview"}`,
                )}
                onClick={() => {
                  editPlayerReview(index);
                }}
                color="inherit"
              >
                <CommentIcon
                  color={hasReviewDetailError ? "error" : hasReviewDetails ? "primary" : "disabled"}
                />
              </IconButton>
            </Tooltip>
          </Box>
        </Stack>
      </Stack>
      <Divider sx={{ width: "100%" }} />
      {EditPlayerReviewUI}
    </Stack>
  );
};
