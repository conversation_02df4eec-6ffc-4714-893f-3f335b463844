import { FC, useState } from "react";
import { useTranslation } from "react-i18next";

import { Player, TrainingSessionPlayerReview } from "@mio/helpers";
import {
  Stack,
  Box,
  Typography,
  Divider,
  Button,
  ArrowUpwardIcon,
  ArrowDownwardIcon,
  StarRating,
} from "@mio/ui";

import {
  SortingOption,
  SortingOrder,
  formatPercent,
  getPlayerTotals,
  percentToRating,
  percentToRatingFormatted,
} from "./insights-helpers";

type Props = {
  reviews: TrainingSessionPlayerReview.ExtendedReview[];
};

const gridColumns = "1.5fr 1fr 1fr 1fr";

const gridStyle = {
  display: {
    md: "grid",
  },
  gap: {
    sm: 4,
  },
  gridTemplateColumns: {
    md: gridColumns,
  },
  alignItems: "center",
};

const getHeaderIcon = (
  activeSortOption: SortingOption,
  sortOrder: SortingOrder,
  target: SortingOption,
) => {
  return activeSortOption === target ? (
    sortOrder === SortingOrder.Ascending ? (
      <ArrowUpwardIcon />
    ) : (
      <ArrowDownwardIcon />
    )
  ) : null;
};

const ReviewBreakdown: FC<Props> = ({ reviews }) => {
  const { t } = useTranslation();
  const [sortOption, setSortOption] = useState(SortingOption.Reviews);
  const [sortOrder, setSortOrder] = useState(SortingOrder.Descending);

  const playerTotals = getPlayerTotals(reviews, sortOption, sortOrder);

  const handleSort = (option: SortingOption) => {
    if (option === sortOption) {
      setSortOrder(
        sortOrder === SortingOrder.Ascending ? SortingOrder.Descending : SortingOrder.Ascending,
      );
    } else {
      setSortOption(option);
      setSortOrder(SortingOrder.Descending);
    }
  };

  return (
    <Stack gap={3}>
      <Box component="header" sx={gridStyle}>
        <Button
          variant="text"
          sx={{ justifyContent: "flex-start" }}
          startIcon={getHeaderIcon(sortOption, sortOrder, SortingOption.Name)}
          onClick={() => handleSort(SortingOption.Name)}
        >
          <Typography fontWeight="bold">{t("common.name")}</Typography>
        </Button>
        <Button
          variant="text"
          sx={{ justifyContent: "flex-start" }}
          startIcon={getHeaderIcon(sortOption, sortOrder, SortingOption.Reviews)}
          onClick={() => handleSort(SortingOption.Reviews)}
        >
          <Typography fontWeight="bold">{t("training.reviews")}</Typography>
        </Button>
        <Button
          variant="text"
          sx={{ justifyContent: "flex-start" }}
          startIcon={getHeaderIcon(sortOption, sortOrder, SortingOption.Attendance)}
          onClick={() => handleSort(SortingOption.Attendance)}
        >
          <Typography fontWeight="bold">{t("training.attendance")}</Typography>
        </Button>
        <Button
          variant="text"
          sx={{ justifyContent: "flex-start" }}
          startIcon={getHeaderIcon(sortOption, sortOrder, SortingOption.Performance)}
          onClick={() => handleSort(SortingOption.Performance)}
        >
          <Typography fontWeight="bold">{t("training.performance")}</Typography>
        </Button>
      </Box>
      <Stack gap={1}>
        {playerTotals.map((record) => {
          return (
            <Box key={record.player.id} sx={{ py: 2 }}>
              <Stack sx={gridStyle}>
                <Typography>{Player.getFullName(record.player)}</Typography>
                <Typography>{record.reviews}</Typography>
                <Stack direction="row" gap={1} alignItems="center">
                  <Typography>{formatPercent(record.attendance)}</Typography>
                </Stack>
                <Stack direction="row" gap={1} alignItems="center">
                  <StarRating
                    value={percentToRating(record.performance)}
                    readOnly
                    precision={0.1}
                  />
                  <Typography>{percentToRatingFormatted(record.performance)}</Typography>
                </Stack>
              </Stack>
              <Divider sx={{ mt: 1 }} />
            </Box>
          );
        })}
      </Stack>
    </Stack>
  );
};
export default ReviewBreakdown;
