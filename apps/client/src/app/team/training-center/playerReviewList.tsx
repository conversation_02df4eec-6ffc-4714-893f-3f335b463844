import { FC } from "react";
import { useTranslation } from "react-i18next";

import { <PERSON>, <PERSON>ack, Typo<PERSON>, Button, Chip, AddIcon, Alert, EditIcon, Card } from "@mio/ui";
import { TrainingSessionReview } from "@mio/helpers";

import { useEditPlayerReview } from "./crud/useEditPlayerReview";
import { usePlayerProfile } from "../../shared";
import { useCreatePlayerReview } from "./crud/useCreatePlayerReview";
import { usePlayerReviews } from "../../shared/playerReviews";

const gridColumns = "1.5fr 1fr 1fr 1fr";

const gridStyle = {
  display: {
    md: "grid",
  },
  gap: {
    sm: 4,
  },
  gridTemplateColumns: {
    md: gridColumns,
  },
  alignItems: "center",
};

type Props = {
  review: TrainingSessionReview.ExtendedReview;
};

export const PlayerReviewList: FC<Props> = ({ review }) => {
  const { t } = useTranslation();
  const { EditPlayerReviewUI, editPlayerReview } = useEditPlayerReview(review);
  const { CreatePlayerReviewUI, createPlayerReview } = useCreatePlayerReview(review);
  const { PlayerProfileUI, show } = usePlayerProfile();

  const playersWithReviews = new Set(
    review.playerReviews.map((playerReview) => playerReview.playerId),
  );

  const playerReviews = usePlayerReviews();

  const playersWithoutReviews = review.players.filter(
    (player) => !playersWithReviews.has(player.id),
  );

  const {
    toDisplayTrainingSessionAttendanceColor,
    toAttendanceLabel,
    toPerformanceLabel,
    toDisplayPlayerPerformanceBasicRatingsColor,
  } = playerReviews;

  return (
    <Stack gap={3}>
      {review.playerReviews.map((playerReview) => {
        const player = review.players.find((elem) => elem.id === playerReview.playerId);

        if (!player) {
          return <Alert severity="warning">{t("training.player-not-found")}</Alert>;
        }

        return (
          <Card sx={{ p: 2 }} key={playerReview.id}>
            <Box sx={gridStyle}>
              <Button
                variant="text"
                onClick={() =>
                  show({
                    firstName: "Peter",
                    lastName: "Crouch",
                    strengths: ["Finishing", "Jumping", "Heading", "Bicycle kicks"],
                    weaknesses: ["Speed", "Dribbling", "Passing"],
                    improvedOn: ["Dribbling"],
                    lastTwoWeeks: ["Finishing", "Dribbling"],
                  })
                }
              >
                {player.firstName} {player.lastName}
              </Button>
              <Chip
                label={toAttendanceLabel[playerReview.attendance] ?? "-"}
                color={toDisplayTrainingSessionAttendanceColor[playerReview.attendance]}
              />
              <Chip
                label={toPerformanceLabel[playerReview.rating] ?? "-"}
                color={toDisplayPlayerPerformanceBasicRatingsColor[playerReview.rating]}
              />
              <Button
                variant="outlined"
                sx={{ ml: "auto" }}
                onClick={() =>
                  editPlayerReview({
                    player,
                    review: playerReview,
                  })
                }
                startIcon={<EditIcon />}
              >
                {t("training.edit-review")}
              </Button>
            </Box>
          </Card>
        );
      })}
      {playersWithoutReviews.map((player) => (
        <Card sx={{ p: 2 }} key={player.id}>
          <Box sx={gridStyle} key={player.id}>
            <Typography>
              {player.firstName} {player.lastName}
            </Typography>
            <Chip label={"-"} />
            <Chip label={"-"} />
            <Button
              variant="outlined"
              sx={{ ml: "auto" }}
              onClick={() => createPlayerReview(player)}
              startIcon={<AddIcon />}
            >
              {t("training.add-review")}
            </Button>
          </Box>
        </Card>
      ))}
      {EditPlayerReviewUI}
      {CreatePlayerReviewUI}
      <PlayerProfileUI />
    </Stack>
  );
};
