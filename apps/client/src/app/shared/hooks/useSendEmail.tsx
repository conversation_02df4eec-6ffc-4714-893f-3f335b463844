import { FC, useState } from "react";
import { filter } from "lodash/fp";
import { useTranslation } from "react-i18next";

import {
  Box,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Alert,
  useTheme,
  useMediaQuery,
  useSendEmail,
  useGetEmailTemplates,
  useOrganization,
  LocalLoader,
  LocalError,
  InputLabel,
  Select,
  MenuItem,
} from "@mio/ui";
import RichTextEditor from "../components/RichTextEditor";
import { Email, EmailTemplate, OrganizationId, isError } from "@mio/helpers";

type Props = {
  recipients: Array<Email | undefined>;
  onClose: () => void;
  organizationId: OrganizationId;
};

const SendEmailModal: FC<Props> = ({ onClose, recipients, organizationId }) => {
  const { t } = useTranslation();
  const [emailBody, setEmailBody] = useState<string>("");
  const [subject, setSubject] = useState<string>("");
  const theme = useTheme();
  const isOnSmallScreen = useMediaQuery(theme.breakpoints.down("md"));

  const query = useGetEmailTemplates(organizationId);
  const [selectedEmailTemplate, setSelectedEmailTemplate] = useState<EmailTemplate | undefined>();

  const sendEmail = useSendEmail();

  const handleSubmit = (source: unknown) => {
    const parsed = Email.toEmailDto(source);

    if (!isError(parsed)) {
      sendEmail.mutate(parsed);
    } else {
      // TODO: we can save the errors into a variable and display them if we wish so
    }
  };

  return (
    <Dialog open={!!recipients} fullWidth={!isOnSmallScreen} fullScreen={isOnSmallScreen}>
      <DialogTitle id="confirmation-dialog-title" sx={{ mb: 2 }}>
        {t("email.send.title")}
      </DialogTitle>

      <DialogContent>
        <Box mb={2}>
          <TextField
            fullWidth
            label={t("email.send.subject")}
            value={subject}
            onChange={(e: React.ChangeEvent<HTMLInputElement>) => setSubject(e.target.value)}
          />
        </Box>

        <Box mb={2}>
          {query.isLoading && <LocalLoader message={t("email.send.template.loading")} />}

          {query.isError && <LocalError message={t("email.send.template.load-error")} />}

          {query.data && (
            <Box>
              <InputLabel id="template-label">{t("email.send.template.label")}</InputLabel>
              <Select
                fullWidth
                labelId="template-label"
                value={selectedEmailTemplate?.id || "none"}
                onChange={(event) => {
                  const template = query.data.find((temp) => temp.id === event.target.value);
                  setSelectedEmailTemplate(template);
                  setEmailBody(template?.body || "");
                }}
              >
                <MenuItem value="none" key="none">
                  {t("email.send.template.none")}
                </MenuItem>
                {query.data.map((template) => (
                  <MenuItem value={template.id} key={template.id}>
                    {template.name}
                  </MenuItem>
                ))}
              </Select>
            </Box>
          )}
        </Box>

        <Box mb={2} sx={{ border: "1px solid lightgray", minHeigh: "300px" }}>
          <RichTextEditor
            value={selectedEmailTemplate?.body || ""}
            onChange={(value) => {
              setEmailBody(value);
            }}
            customStyle={{ height: "200px" }}
          />
          {sendEmail.isSuccess && <Alert severity="success">{t("email.send.success")}</Alert>}
          {sendEmail.isError && <Alert severity="error">{t("email.send.error")}</Alert>}
        </Box>
      </DialogContent>
      <DialogActions>
        <Button variant="outlined" onClick={onClose}>
          {t("email.send.close")}
        </Button>

        <Button
          variant="outlined"
          onClick={() =>
            handleSubmit({ body: emailBody, to: filter<Email>(Boolean)(recipients), subject })
          }
          loading={sendEmail.isLoading}
        >
          {t("email.send.send")}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export const useEmailModal = (sendTo?: Array<Email | undefined> | undefined) => {
  const [recipients, setRecipient] = useState<Array<Email | undefined> | undefined>(sendTo);
  const organization = useOrganization();

  return {
    SendEmailUI: () =>
      recipients ? (
        <SendEmailModal
          recipients={recipients}
          onClose={() => setRecipient(undefined)}
          organizationId={organization.id}
        />
      ) : null,
    show: (recipient: Array<Email | undefined> | undefined) => setRecipient(recipient),
  };
};
