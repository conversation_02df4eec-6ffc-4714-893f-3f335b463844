import { FC } from "react";
import { useTranslation } from "react-i18next";
import { startCase } from "lodash/fp";

import {
  Box,
  Typography,
  TextField,
  Button,
  LocalError,
  visuallyHidden,
  Stack,
  FormControl,
  FormControlLabel,
  FormGroup,
  Checkbox,
  FormHelperText,
  useOrganization,
  useFormResolver,
} from "@mio/ui";
import { APIError, ErrorMessages, PermissionsModule, Primitive } from "@mio/helpers";
import { useForm } from "react-hook-form";

type DataShape = Primitive<PermissionsModule.Role.SaveDto>;

type Props = {
  role?: PermissionsModule.Role.SaveDto;
  formTitle: string;
  onSubmit: (data: unknown) => void;
  loading?: boolean;
  serverError?: APIError | null;
};

const useSaveRoleForm = (defaultValues: Partial<DataShape> = {}) => {
  const formResolver = useFormResolver<DataShape>(PermissionsModule.Role.Role.toSaveDto);

  const formMethods = useForm<DataShape>({
    defaultValues,
    resolver: formResolver,
  });

  const actions = formMethods.watch("actions") || [];

  const toggleAction = (action: PermissionsModule.Action.Actions) => {
    const isAddedAlready = actions.includes(action);
    let result: PermissionsModule.Action.Actions[] = [];

    if (isAddedAlready) {
      result = actions.filter((elem) => elem !== action);
    } else {
      result = [...actions, action];
    }

    formMethods.setValue("actions", result, {
      shouldDirty: true,
      shouldTouch: true,
      shouldValidate: true,
    });
  };

  const { errors, submitCount } = formMethods.formState;
  const hasSubmitted = submitCount > 0;

  const data = formMethods.watch();

  return {
    formMethods,
    hasSubmitted,
    errors: hasSubmitted ? errors : {},
    actions,
    toggleAction,
    data,
  };
};

export const SaveRoleForm: FC<Props> = ({ onSubmit, loading, serverError, formTitle, role }) => {
  const { t } = useTranslation();
  const organization = useOrganization();

  const { formMethods, errors, hasSubmitted, actions, toggleAction, data } = useSaveRoleForm({
    organizationId: role?.organizationId || organization.id,
    actions: role?.actions || [],
    name: role?.name || "",
    description: role?.description || "",
  });

  return (
    <Stack
      component="form"
      gap={2}
      onSubmit={formMethods.handleSubmit(() => {
        onSubmit(data);
      })}
    >
      <Box component="legend" sx={visuallyHidden}>
        <Typography variant="h2" component="h1">
          {formTitle}
        </Typography>
      </Box>
      <Box>
        <TextField
          fullWidth
          required
          label={t("roles.name")}
          name="name"
          inputProps={{ ...formMethods.register("name") }}
          helperText={(hasSubmitted && errors.name?.message) || ""}
          error={hasSubmitted && !!errors.name?.message}
          aria-invalid={hasSubmitted && !!errors.name?.message}
        />
      </Box>
      <Box>
        <TextField
          fullWidth
          label={t("roles.description")}
          name="description"
          multiline
          minRows={3}
          inputProps={{ ...formMethods.register("description"), defaultValue: undefined }}
          helperText={(hasSubmitted && errors.description?.message) || ""}
          error={hasSubmitted && !!errors.description?.message}
          aria-invalid={hasSubmitted && !!errors.description?.message}
          defaultValue={undefined}
        />
      </Box>
      <FormControl variant="standard">
        <FormGroup>
          {Object.values(PermissionsModule.Action.Actions).map((action) => (
            <FormControlLabel
              key={action}
              control={
                <Checkbox
                  checked={actions.includes(action)}
                  onChange={() => toggleAction(action)}
                  value={action}
                />
              }
              label={startCase(action)}
            />
          ))}
        </FormGroup>
        {errors.actions && <FormHelperText error={true}>{errors.actions.message}</FormHelperText>}
      </FormControl>
      {serverError && (
        <Box mt={2}>
          <LocalError
            message={
              serverError.message === ErrorMessages.EntityAlreadyExists
                ? t("roles.role-already-exists")
                : t("common.something-went-wrong")
            }
          />
        </Box>
      )}

      <Box sx={{ textAlign: "center", mt: 4 }}>
        <Button type="submit" loading={loading} variant="contained" color="secondary" size="large">
          {t("common.save")}
        </Button>
      </Box>
    </Stack>
  );
};
