import { FC } from "react";
import { Controller } from "react-hook-form";
import { useTranslation } from "react-i18next";

import {
  Stack,
  TextField,
  Button,
  Box,
  Alert,
  useOrganization,
  useProvidedCurrentUser,
  FormControl,
  Select,
  MenuItem,
  InputLabel,
} from "@mio/ui";
import {
  APIError,
  CreateInviteDto,
  ErrorMessages,
  PermissionsModule,
  Primitive,
} from "@mio/helpers";

import { useInviteForm } from "./useInviteForm";

type DataShape = Primitive<CreateInviteDto>;

type Props = {
  onSubmit: (data: unknown) => void;
  loading?: boolean;
  serverError: APIError | null;
  permissionRoles: PermissionsModule.Role.Role[];
};

const CreateInviteForm: FC<Props> = ({ loading, serverError, onSubmit, permissionRoles }) => {
  const { t } = useTranslation();
  const user = useProvidedCurrentUser();
  const organization = useOrganization();

  const { formMethods, errors, control } = useInviteForm(
    organization.id,
    user.authentication.email,
  );

  return (
    <Stack component="form" gap={3} onSubmit={formMethods.handleSubmit(onSubmit)}>
      <Box>
        <TextField
          required
          fullWidth
          aria-required
          type="email"
          label={t("common.email")}
          name="email"
          inputProps={{ ...formMethods.register("email") }}
          helperText={errors.email?.message || ""}
          error={!!errors.email?.message}
          aria-invalid={!!errors.email?.message}
        />
      </Box>

      {serverError && serverError.message !== ErrorMessages.InviteAlreadyRedeemed && (
        <Box>
          <Alert severity="error">{t("common.something-went-wrong")}</Alert>
        </Box>
      )}

      {serverError && serverError.message === ErrorMessages.InviteAlreadyRedeemed && (
        <Box>
          <Alert severity="error">{t("users.email-already-registered")}</Alert>
        </Box>
      )}

      <FormControl>
        <InputLabel htmlFor="invite-permissions">{t("users.permissions")}</InputLabel>
        <Controller<DataShape>
          name="permissions"
          defaultValue={PermissionsModule.PermissionEntity.Type.Owner}
          control={control}
          render={(params) => (
            <Select
              fullWidth
              inputProps={{ ...params.field }}
              label={t("users.permissions")}
              id="invite-permissions"
            >
              <MenuItem value={PermissionsModule.PermissionEntity.Type.Owner}>
                {t("users.roles.owner")}
              </MenuItem>

              {permissionRoles.map((role) => (
                <MenuItem key={role.id} value={role.id}>
                  {role.name}
                </MenuItem>
              ))}
            </Select>
          )}
        />
      </FormControl>

      <Stack justifyContent="center" direction="row">
        <Button type="submit" loading={loading} variant="contained" size="large" color="secondary">
          {t("users.send")}
        </Button>
      </Stack>
    </Stack>
  );
};

export default CreateInviteForm;
