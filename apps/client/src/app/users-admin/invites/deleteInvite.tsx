import { FC, useState } from "react";
import { useTranslation } from "react-i18next";

import {
  Confirmation,
  Button,
  Snackbar,
  Alert,
  Typography,
  DeleteIcon,
  useDeleteInvite,
} from "@mio/ui";
import { Invite, OrganizationId } from "@mio/helpers";

type Props = {
  invite: Invite;
  orgId: OrganizationId;
  onDelete: () => void;
};

const DeleteInvite: FC<Props> = ({ invite, orgId, onDelete }) => {
  const { t } = useTranslation();
  const deleteRequest = useDeleteInvite();
  const [open, toggleOpen] = useState(false);
  const [result, setResult] = useState<"success" | "fail" | undefined>(undefined);

  return (
    <>
      <Button
        variant="outlined"
        onClick={() => toggleOpen(true)}
        loading={deleteRequest.isLoading}
        startIcon={<DeleteIcon />}
      >
        {t("common.delete")}
      </Button>
      <Confirmation
        open={open}
        title={t("common.please-confirm")}
        content={
          <Typography>{t("users.delete-invite-confirmation", { email: invite.email })}</Typography>
        }
        onConfirm={() =>
          deleteRequest.mutate(
            { inviteId: invite.id, orgId },
            {
              onSuccess: () => {
                setResult("success");
                onDelete();
              },
              onError: () => {
                setResult("fail");
              },
            },
          )
        }
        onClose={() => toggleOpen(false)}
      />

      <Snackbar
        open={result === "success"}
        autoHideDuration={2000}
        onClose={() => setResult(undefined)}
      >
        <Alert severity="success">{t("users.invite-deleted")}</Alert>
      </Snackbar>

      <Snackbar
        open={result === "fail"}
        autoHideDuration={2000}
        onClose={() => setResult(undefined)}
      >
        <Alert severity="error">{t("users.failed-to-delete-invite")}</Alert>
      </Snackbar>
    </>
  );
};

export default DeleteInvite;
