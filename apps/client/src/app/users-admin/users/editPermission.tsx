import { useState, useEffect } from "react";
import { useTranslation } from "react-i18next";

import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  ToggleSnackbar,
  Modal,
  Typography,
  Stack,
  Box,
  useOrganization,
  useOrganizationUsersCrud,
  useUpdatePermission,
} from "@mio/ui";
import { PermissionsModule, Coach, TeamId } from "@mio/helpers";
import { TeamPicker } from "../../teams-admin";

type Params = {
  user: Coach<"populated">;
  permission: PermissionsModule.PermissionEntity.CustomPermission;
};

export const useTeamsEdit = () => {
  const [teams, setTeams] = useState<TeamId[]>([]);
  const [params, setParams] = useState<Params | undefined>(undefined);
  const organization = useOrganization();
  const { t } = useTranslation();

  const { updatePermission } = useOrganizationUsersCrud(organization.id);
  const editPermissions = useUpdatePermission();

  useEffect(() => {
    if (params?.permission) {
      setTeams(params.permission.teamIds || []);
      editPermissions.reset();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [params]);

  return {
    editPermission: (payload: Params) => setParams(payload),
    EditUI: (
      <>
        <Modal
          open={!!params}
          title={<Typography>{t("permissions.edit-teams")}</Typography>}
          onClose={() => {
            setParams(undefined);
          }}
          fullWidth
          content={
            <form
              onSubmit={(event) => {
                event.preventDefault();
                event.stopPropagation();

                if (params) {
                  editPermissions.mutate(
                    {
                      permissionId: params.permission.id,
                      organizationId: organization.id,
                      dto: {
                        teamIds: teams,
                      },
                    },
                    {
                      onSuccess: (result) => {
                        updatePermission(params.user, result);
                        setParams(undefined);
                        setTeams([]);
                      },
                    },
                  );
                }
              }}
            >
              <Stack gap={2}>
                <TeamPicker selectedTeams={teams} onChange={(value) => setTeams(value)} />

                <Box>
                  <Button loading={editPermissions.isLoading} type="submit" variant="contained">
                    {t("common.save")}
                  </Button>
                </Box>
              </Stack>
            </form>
          }
        />

        <ToggleSnackbar open={editPermissions.isSuccess}>
          <Alert severity="success">{t("permissions.permissions-updated")}</Alert>
        </ToggleSnackbar>

        <ToggleSnackbar open={editPermissions.isError}>
          <Alert severity="error">{t("common.something-went-wrong")}</Alert>
        </ToggleSnackbar>
      </>
    ),
  };
};
