import { FC, FormEvent, useEffect, useState } from "react";
import { useTranslation } from "react-i18next";

import {
  <PERSON><PERSON>,
  Button,
  Modal,
  Typography,
  FormControl,
  Select,
  InputLabel,
  MenuItem,
  LocalError,
  AddIcon,
  useCreatePermission,
  useOrganization,
  useOrganizationUsersCrud,
  FormHelperText,
} from "@mio/ui";
import { ErrorMessages, PermissionsModule, Coach } from "@mio/helpers";

type Props = {
  roles: PermissionsModule.Role.Role[];
  user: Coach<"populated">;
};

const AddPermission: FC<Props> = ({ roles, user }) => {
  const { t } = useTranslation();
  const [showForm, toggleForm] = useState(false);
  const [hasSubmitted, toggleHasSubmitted] = useState(false);
  const [selectedRole, setRole] = useState<PermissionsModule.Role.RoleId | undefined>(undefined);
  const organization = useOrganization();
  const { addPermission } = useOrganizationUsersCrud(organization.id);

  const createPermission = useCreatePermission();

  useEffect(() => {
    return () => {
      if (!showForm) {
        createPermission.reset();
      }
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [showForm]);

  const handleSubmit = (event: FormEvent<HTMLFormElement>) => {
    event.preventDefault();
    event.stopPropagation();
    toggleHasSubmitted(true);

    if (selectedRole) {
      createPermission.mutate(
        {
          roleId: selectedRole,
          organizationId: organization.id,
          profileId: user.profile.id,
        },
        {
          onSuccess: (response) => {
            addPermission(user, response);
            toggleForm(false);
          },
        },
      );
    }
  };

  const hasError = hasSubmitted && !selectedRole;

  return (
    <>
      <Stack direction="row" justifyContent="center">
        <Button
          variant="text"
          onClick={() => toggleForm(true)}
          size="large"
          startIcon={<AddIcon />}
        >
          {t("permissions.add-permission")}
        </Button>
      </Stack>

      <Modal
        open={showForm}
        title={<Typography>{t("permissions.add-permission")}</Typography>}
        onClose={() => toggleForm(false)}
        fullWidth={true}
        content={
          <form onSubmit={handleSubmit}>
            <Stack gap={2} p={1}>
              <FormControl error={hasError}>
                <InputLabel id="role-label" aria-required>
                  {t("permissions.role")}
                </InputLabel>
                <Select
                  fullWidth
                  aria-required
                  labelId="role-label"
                  label={t("permissions.role")}
                  name="role"
                  value={selectedRole || ""}
                  onChange={(event) => {
                    setRole(event.target.value as PermissionsModule.Role.RoleId);
                  }}
                >
                  {roles.map((role) => (
                    <MenuItem key={role.id} value={role.id}>
                      {role.name}
                    </MenuItem>
                  ))}
                </Select>

                {hasError && <FormHelperText>{t("permissions.select-role")}</FormHelperText>}
              </FormControl>

              <Stack direction="row" justifyContent="center">
                <Button
                  loading={createPermission.isLoading}
                  type="submit"
                  variant="contained"
                  color="secondary"
                  size="large"
                >
                  {t("common.submit")}
                </Button>
              </Stack>

              {createPermission.isError && (
                <Stack>
                  <LocalError
                    message={
                      createPermission.error.message === ErrorMessages.EntityAlreadyExists
                        ? t("permissions.permission-exists")
                        : t("common.something-went-wrong")
                    }
                  />
                </Stack>
              )}
            </Stack>
          </form>
        }
      />
    </>
  );
};

export default AddPermission;
