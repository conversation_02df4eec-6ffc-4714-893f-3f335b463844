import { FC } from "react";
import { useNavigate } from "react-router-dom";
import { useTranslation } from "react-i18next";

import {
  Select,
  MenuItem,
  Box,
  FormLabel,
  LocalError,
  LocalLoader,
  useTheme,
  useGetTeams,
  useOrganization,
  useTeamId,
} from "@mio/ui";

export const TeamSelector: FC = () => {
  const { t } = useTranslation();
  const organization = useOrganization();
  const theme = useTheme();
  const teams = useGetTeams(organization.id);
  const selectedTeam = useTeamId() || "";
  const navigate = useNavigate();

  if (teams.isLoading) {
    return <LocalLoader message={t("sidebar.team-selector.loading")} />;
  }

  if (teams.isError) {
    return <LocalError message={t("sidebar.team-selector.load-error")} />;
  }

  return (
    <Box sx={{ color: theme.palette.primary.contrastText }}>
      <FormLabel sx={{ marginBottom: 1, display: "block", color: "inherit" }}>
        {t("sidebar.team-selector.active-team")}
      </FormLabel>
      <Select
        value={selectedTeam}
        id="team-picker"
        fullWidth
        sx={(theme) => ({
          color: "inherit",
          ".MuiOutlinedInput-notchedOutline": {
            borderColor: "inherit",
          },
          "&.Mui-focused .MuiOutlinedInput-notchedOutline": {
            borderColor: "inherit",
          },
          "&:hover .MuiOutlinedInput-notchedOutline": {
            borderColor: "inherit",
          },
          ".MuiSvgIcon-root ": {
            fill: `${theme.palette.primary.contrastText} !important`,
          },
        })}
        onChange={(event) => {
          const teamId = event.target.value;
          if (teamId) {
            const url = `/${organization.id}/teams/${teamId}/players-list`;
            navigate(url);
          }
        }}
      >
        {teams.data?.map((elem) => (
          <MenuItem
            value={elem.id}
            key={elem.id}
            aria-label={t("sidebar.team-selector.change-team", { name: elem.name })}
          >
            {elem.name}
          </MenuItem>
        ))}
      </Select>
    </Box>
  );
};
