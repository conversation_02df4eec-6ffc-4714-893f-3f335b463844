import { FC } from "react";
import { useForm } from "react-hook-form";
import { useTranslation } from "react-i18next";
import { TFunction } from "i18next";
import {
  Box,
  TextField,
  Stack,
  LocalError,
  FormLabel,
  Select,
  MenuItem,
  Button,
  FormControl,
  FormHelperText,
  useFormResolver,
  useOrganization,
  useProvidedCurrentProfile,
  Typography,
} from "@mio/ui";
import {
  EmailTemplate,
  Primitive,
  CreateEmailTemplateDto,
  EmailTemplateVisibility,
} from "@mio/helpers";
import RichTextEditor from "../shared/components/RichTextEditor";

export type DataShape = Partial<Primitive<CreateEmailTemplateDto>>;

type Props = {
  template?: EmailTemplate;
  onSubmit: (data: DataShape) => void;
  loading?: boolean;
  error?: boolean;
};

export const VisibilityText = (t: TFunction, visibility: EmailTemplateVisibility) => {
  switch (visibility) {
    case EmailTemplateVisibility.Company:
      return t("email-templates.visibility-company");
    case EmailTemplateVisibility.Private:
      return t("email-templates.visibility-private");
  }
};

export const UpsertEmailTemplate: FC<Props> = ({ template, onSubmit, loading, error }) => {
  const { t } = useTranslation();
  const profile = useProvidedCurrentProfile();
  const org = useOrganization();

  const formResolver = useFormResolver<DataShape>(EmailTemplate.toUpsertDto);
  const { handleSubmit, watch, formState, register, setValue } = useForm<DataShape>({
    defaultValues: {
      ...template,
      ownerId: profile.user,
      organizationId: org.id,
      visibility: template?.visibility ?? EmailTemplateVisibility.Private,
    },
    resolver: formResolver,
  });

  const { errors } = formState;
  const values = watch();

  return (
    <Stack gap={3} mt={1} component="form" onSubmit={handleSubmit(onSubmit)}>
      <FormControl>
        <TextField
          fullWidth
          aria-required
          id="name"
          label={t("email-templates.template-name")}
          name="name"
          inputProps={{ ...register("name") }}
          helperText={errors.name?.message || ""}
          error={!!errors.name?.message}
          aria-invalid={!!errors.name?.message}
        />
      </FormControl>

      <FormControl>
        <TextField
          fullWidth
          aria-required
          id="subject"
          label={t("email-templates.subject")}
          name="subject"
          inputProps={{ ...register("subject") }}
          helperText={errors.subject?.message || ""}
          error={!!errors.subject?.message}
          aria-invalid={!!errors.subject?.message}
        />
      </FormControl>

      <FormControl>
        <FormLabel>{t("email-templates.visibility")}</FormLabel>
        <Select<EmailTemplateVisibility>
          fullWidth
          aria-required
          id="visibility"
          label={t("email-templates.visibility")}
          name="visibility"
          inputProps={{ ...register("visibility") }}
          error={!!errors.visibility?.message}
          aria-invalid={!!errors.visibility?.message}
          defaultValue={formState.defaultValues?.visibility as EmailTemplateVisibility}
        >
          <MenuItem value={EmailTemplateVisibility.Private}>
            {t("email-templates.visibility-private")}
          </MenuItem>
          <MenuItem value={EmailTemplateVisibility.Company}>
            {t("email-templates.visibility-company")}
          </MenuItem>
        </Select>
        <Typography variant="body2" color="grey" sx={{ mt: 1 }}>
          {t("email-templates.visibility-description")}
        </Typography>
        {errors.visibility?.message && (
          <FormHelperText>{errors.visibility?.message}</FormHelperText>
        )}
      </FormControl>

      <FormControl>
        <FormLabel>{t("email-templates.body")}</FormLabel>
        <RichTextEditor
          value={values.body || ""}
          onChange={(value) => {
            setValue("body", value, {
              shouldDirty: true,
              shouldTouch: true,
              shouldValidate: true,
            });
          }}
        />
      </FormControl>

      <Box gap={2} sx={{ display: "flex", justifyContent: "center" }}>
        <Button type="submit" variant="contained" loading={loading}>
          {t("common.save")}
        </Button>
      </Box>

      {error && <LocalError message={t("common.error")} />}
    </Stack>
  );
};
