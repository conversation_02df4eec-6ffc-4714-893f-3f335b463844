import { FC } from "react";

import { EmailTemplate } from "@mio/helpers";
import { Typo<PERSON>, Card, Chip, Box, EditIcon, IconButton, Tooltip, DeleteIcon } from "@mio/ui";
import { VisibilityText } from "./upsert.form";
import { useTranslation } from "react-i18next";

type Props = {
  template: EmailTemplate;
  onEdit: () => void;
  onDelete: () => void;
};

const gridColumns = "1.5fr 1.5fr 1fr";

const gridStyle = {
  display: {
    md: "grid",
  },
  gap: {
    sm: 4,
  },
  gridTemplateColumns: {
    md: gridColumns,
  },
  alignItems: "center",
};

export const TemplateItem: FC<Props> = ({ template, onEdit, onDelete }) => {
  const { t } = useTranslation();

  return (
    <Card sx={{ p: 2 }}>
      <Box sx={gridStyle}>
        <Box>
          <Typography>{template.name}</Typography>
          <Typography variant="body2" color="grey">
            {t("email-templates.subject")}: {template.subject ?? t("email-templates.no-subject")}
          </Typography>
        </Box>
        <Chip label={VisibilityText(t, template.visibility)} sx={{ m: "auto" }} />
        <Box ml="auto">
          <Tooltip title="Edit template">
            <IconButton onClick={onEdit} aria-label={t("email-templates.edit-template")}>
              <EditIcon />
            </IconButton>
          </Tooltip>
          <Tooltip title={t("email-templates.delete-template")}>
            <IconButton aria-label={t("email-templates.delete-template")} onClick={onDelete}>
              <DeleteIcon />
            </IconButton>
          </Tooltip>
        </Box>
      </Box>
    </Card>
  );
};
