import { FC } from "react";
import { useForm } from "react-hook-form";
import { useTranslation } from "react-i18next";

import {
  Box,
  Typography,
  DatePicker,
  TextField,
  Stack,
  LocalError,
  Button,
  useOrganizationId,
  useFormResolver,
} from "@mio/ui";
import {
  APIError,
  CreateSeasonDto,
  CustomDate,
  CustomNumber,
  Primitive,
  Season,
} from "@mio/helpers";

import { BackButton } from "../shared";

type DataShape = Partial<Primitive<CreateSeasonDto> & { id?: string }>;

type Props = {
  season?: Season;
  onSubmit: (data: DataShape) => void;
  loading?: boolean;
  serverError?: APIError | null;
};

export const UpsertSeasonForm: FC<Props> = ({ season, onSubmit, loading, serverError }) => {
  const { t } = useTranslation();
  const organizationId = useOrganizationId();
  const formResolver = useFormResolver<DataShape>(Season.toUpsertDto);

  const { register, handleSubmit, setValue, watch, formState } = useForm<DataShape>({
    defaultValues: {
      id: season?.id,
      name: season?.name,
      objective: season?.objective,
      startDate: season?.startDate || CustomDate.toPastYear(CustomNumber.castToPositiveInteger(7)),
      endDate: season?.endDate || CustomDate.now(),
      organizationId,
    },
    resolver: formResolver,
  });

  const startDate = watch("startDate");
  const endDate = watch("endDate");

  const { errors, submitCount } = formState;
  const hasSubmitted = submitCount > 0;
  const title = season
    ? t("seasons.edit-season-title", { name: season.name })
    : t("seasons.add-new-season");

  return (
    <Box
      width={1}
      component="form"
      onSubmit={handleSubmit(onSubmit)}
      sx={{ width: "100%", bgcolor: "background.paper" }}
    >
      <Stack direction="row" alignItems="center" mb={4} component="header" flexWrap="wrap" gap={1}>
        <Box>
          <BackButton path="../seasons">{t("seasons.back-to-seasons")}</BackButton>
        </Box>
        <Box component="legend" display="block" sx={{ flexGrow: 1, pr: 5 }}>
          <Typography variant="h5" component="h1" textAlign="center">
            {title}
          </Typography>
        </Box>
      </Stack>

      <Box mt={2}>
        <TextField
          fullWidth
          aria-required
          id="name"
          label={t("seasons.name")}
          name="name"
          inputProps={{ ...register("name") }}
          helperText={(hasSubmitted && errors.name?.message) || ""}
          error={hasSubmitted && !!errors.name?.message}
          aria-invalid={hasSubmitted && !!errors.name?.message}
        />
      </Box>

      <Box mt={2}>
        <TextField
          fullWidth
          aria-required
          id="objective"
          name="objective"
          label={t("seasons.objective")}
          placeholder={t("seasons.objective-placeholder")}
          inputProps={{ ...register("objective") }}
          helperText={(hasSubmitted && errors.objective?.message) || ""}
          error={hasSubmitted && !!errors.objective?.message}
          aria-invalid={hasSubmitted && !!errors.objective?.message}
        />
      </Box>

      <Box mt={2}>
        <DatePicker
          label={t("seasons.start-date-label")}
          onChange={(newValue) => {
            setValue("startDate", CustomDate.validOrEmpty(newValue), {
              shouldValidate: true,
              shouldDirty: true,
              shouldTouch: true,
            });
          }}
          value={startDate}
          slotProps={{
            textField: {
              fullWidth: true,
              "aria-required": true,
              helperText: (hasSubmitted && errors.startDate?.message) || "",
              error: hasSubmitted && !!errors.startDate?.message,
              "aria-invalid": hasSubmitted && !!errors.startDate?.message,
            },
          }}
        />
      </Box>

      <Box mt={2}>
        <DatePicker
          label={t("seasons.end-date-label")}
          onChange={(newValue) => {
            setValue("endDate", CustomDate.validOrEmpty(newValue), {
              shouldValidate: true,
              shouldDirty: true,
              shouldTouch: true,
            });
          }}
          value={endDate}
          slotProps={{
            textField: {
              fullWidth: true,
              helperText: (hasSubmitted && errors.endDate?.message) || "",
              error: hasSubmitted && !!errors.endDate?.message,
              "aria-invalid": hasSubmitted && !!errors.endDate?.message,
            },
          }}
        />
      </Box>

      <Box mt={5} mb={2} sx={{ display: "flex", justifyContent: "center" }}>
        <Button type="submit" loading={loading} variant="contained" color="secondary" size="large">
          {t("common.save")}
        </Button>
      </Box>

      {serverError && <LocalError message={t("common.error")} />}
    </Box>
  );
};
