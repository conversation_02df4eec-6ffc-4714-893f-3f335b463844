import { FC } from "react";
import { useTranslation } from "react-i18next";

import {
  Typography,
  Box,
  TextField,
  FormLabel,
  FormControl,
  Switch,
  Stack,
  MUILink,
  OpenInNewIcon,
  Button,
} from "@mio/ui";
import { PublicOrganization } from "@mio/helpers";

import { useOrganizationForm } from "./useOrganizationForm";
import { useUpdate } from "./useUpdate";
import { DataShape } from "./types";
import RichTextEditor from "../shared/components/RichTextEditor";
import { environment } from "../../environments/environment";

type Props = {
  organization: PublicOrganization;
};

export const OrganizationForm: FC<Props> = ({ organization }) => {
  const { t } = useTranslation();
  const { formMethods, errors, openApplications, privacyPolicy } =
    useOrganizationForm(organization);

  const { ServerError, UpdateSuccess, isLoading, update } = useUpdate(organization);

  const previewLink = `${environment.playerPortalUrl}/${organization.slug}/apply`;

  const handleSubmit = (data: DataShape) => {
    update(data);
  };

  return (
    <Box component="form" p={2} onSubmit={formMethods.handleSubmit(handleSubmit)}>
      <legend>
        <Typography variant="h4" mb={4} component="h1">
          {t("organization.settings.title")}
        </Typography>
      </legend>

      <Box
        component="fieldset"
        sx={{ borderTop: "none", borderLeft: "none", borderRight: "none", mb: 3 }}
      >
        <Box component="legend" mb={2}>
          <Typography variant="h5" component="h2" mb={1}>
            {t("organization.settings.general.title")}
          </Typography>
        </Box>

        <Box pl={2}>
          <Box mb={2}>
            <TextField
              fullWidth
              label={<FormLabel>{t("organization.settings.general.name")}</FormLabel>}
              id="name"
              name="name"
              inputProps={{ ...formMethods.register("name") }}
              helperText={errors.name?.message || ""}
              error={!!errors.name?.message}
              aria-invalid={!!errors.name?.message}
            />
          </Box>

          <Box mb={2}>
            <TextField
              fullWidth
              label={<FormLabel>{t("organization.settings.general.display-name")}</FormLabel>}
              id="displayName"
              name="displayName"
              inputProps={{ ...formMethods.register("displayName") }}
              helperText={errors.displayName?.message || ""}
              error={!!errors.displayName?.message}
              aria-invalid={!!errors.displayName?.message}
            />
          </Box>

          <Box mb={2}>
            <TextField
              fullWidth
              label={<FormLabel>{t("organization.settings.general.slug")}</FormLabel>}
              id="slug"
              name="slug"
              inputProps={{ ...formMethods.register("slug") }}
              helperText={errors.slug?.message || ""}
              error={!!errors.slug?.message}
              aria-invalid={!!errors.slug?.message}
            />
          </Box>

          <Box mb={2}>
            <TextField
              fullWidth
              label={<FormLabel>{t("organization.settings.general.contact-email")}</FormLabel>}
              type="email"
              name="contactEmail"
              inputProps={{ ...formMethods.register("contactEmail") }}
              helperText={errors.contactEmail?.message || ""}
              error={!!errors.contactEmail?.message}
              aria-invalid={!!errors.contactEmail?.message}
            />
          </Box>

          <Box mb={5} width="100%">
            <FormControl sx={{ width: "100%" }}>
              <FormLabel>{t("organization.settings.general.privacy-policy")}</FormLabel>
              <RichTextEditor
                value={privacyPolicy}
                onChange={(value) => {
                  formMethods.setValue("privacyPolicy", value, {
                    shouldDirty: true,
                    shouldTouch: true,
                    shouldValidate: true,
                  });
                }}
                customStyle={{ width: "100%", border: "1px solid lightgrey" }}
              />
            </FormControl>
          </Box>
        </Box>
      </Box>

      <Box component="fieldset" sx={{ borderTop: "none", borderLeft: "none", borderRight: "none" }}>
        <Stack mb={3} direction="row" alignItems="center" justifyContent="space-between">
          <Box component="legend">
            <Typography variant="h5" component="h2">
              {t("organization.settings.applications.title")}
            </Typography>
          </Box>
          <Box>
            <MUILink
              href={previewLink}
              target="_blank"
              sx={{ display: "flex", alignItems: "center" }}
            >
              <span>{t("organization.settings.applications.preview-form")}</span>
              <OpenInNewIcon sx={{ ml: 1 }} fontSize="small" />
            </MUILink>
          </Box>
        </Stack>

        <Box pl={2}>
          <Box mb={2}>
            <FormControl>
              <FormLabel>{t("organization.settings.applications.open-applications")}</FormLabel>
              <Switch
                checked={openApplications}
                inputProps={{
                  "aria-label": t("organization.settings.applications.open-applications"),
                }}
                onChange={(event) => {
                  formMethods.setValue("applications.open", event.target.checked, {
                    shouldDirty: true,
                    shouldTouch: true,
                    shouldValidate: true,
                  });
                }}
              />
            </FormControl>
          </Box>

          <Box mb={2}>
            <TextField
              fullWidth
              multiline
              minRows={4}
              label={<FormLabel>{t("organization.settings.applications.headline")}</FormLabel>}
              id="applications.headline"
              name="applications.headline"
              inputProps={{ ...formMethods.register("applications.headline") }}
              helperText={errors.applications?.headline?.message || ""}
              error={!!errors.applications?.headline?.message}
              aria-invalid={!!errors.applications?.headline?.message}
            />
          </Box>

          <Box mb={2}>
            <TextField
              fullWidth
              multiline
              minRows={4}
              label={<FormLabel>{t("organization.settings.applications.description")}</FormLabel>}
              id="applications.description"
              name="applications.description"
              inputProps={{ ...formMethods.register("applications.description") }}
              helperText={errors.applications?.description?.message || ""}
              error={!!errors.applications?.description?.message}
              aria-invalid={!!errors.applications?.description?.message}
            />
          </Box>

          <Box mb={2}>
            <TextField
              fullWidth
              multiline
              minRows={4}
              label={
                <FormLabel>{t("organization.settings.applications.closed-message")}</FormLabel>
              }
              id="applications.closedMessage"
              name="applications.closedMessage"
              inputProps={{ ...formMethods.register("applications.closedMessage") }}
              helperText={errors.applications?.closedMessage?.message || ""}
              error={!!errors.applications?.closedMessage?.message}
              aria-invalid={!!errors.applications?.closedMessage?.message}
            />
          </Box>

          <Box mb={2}>
            <TextField
              fullWidth
              multiline
              minRows={4}
              label={
                <FormLabel>{t("organization.settings.applications.success-message")}</FormLabel>
              }
              id="applications.successMessage"
              name="applications.successMessage"
              inputProps={{ ...formMethods.register("applications.successMessage") }}
              helperText={errors.applications?.successMessage?.message || ""}
              error={!!errors.applications?.successMessage?.message}
              aria-invalid={!!errors.applications?.successMessage?.message}
            />
          </Box>
        </Box>
      </Box>

      <Box mt={4}>
        <Box sx={{ display: "flex", justifyContent: "center" }}>
          <Button
            type="submit"
            loading={isLoading}
            disabled={!formMethods.formState.isDirty}
            variant="contained"
            size="large"
            color="secondary"
          >
            {t("organization.settings.save")}
          </Button>
        </Box>

        <Box mt={2}>
          <ServerError />
        </Box>

        <UpdateSuccess />
      </Box>
    </Box>
  );
};
