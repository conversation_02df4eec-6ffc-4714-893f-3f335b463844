import { INestApplication } from "@nestjs/common";
import request from "supertest";

import { AppTestService } from "../app/test/test.service";
import { apiUrls, buildUrlWithParams, Player, PlayerUser } from "@mio/helpers";

/**
 * Authenticates a player and returns the authentication token
 * @param app - The NestJS application instance
 * @param player - The player object containing email
 * @param testService - The test service for creating player users
 * @returns The authentication token
 */
export const authenticatePlayer = async (
  app: INestApplication,
  player: Player,
  testService: AppTestService,
): Promise<{ token: string; playerUser: PlayerUser.PlayerUser }> => {
  if (!player.email) {
    throw new Error("Player email is undefined");
  }

  // Create player user if not already created
  const playerUser = await testService.createPlayerUser(player.email);

  // Login and get token
  const loginResponse = await request(app.getHttpServer())
    .post(buildUrlWithParams(apiUrls.playerLogin, {}, { leadSlash: true }))
    .send({
      email: player.email,
      code: playerUser.authentication.code,
    })
    .expect(201);

  return { token: loginResponse.body.token, playerUser };
};
