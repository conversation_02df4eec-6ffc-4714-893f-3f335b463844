import { addDays } from "date-fns";

import {
  TeamE<PERSON>,
  Team,
  Organization,
  Player,
  RecurringTeamEvent,
  EventScheduling,
  CustomDate,
  Profile,
  StringOfLength,
  ValidDate,
} from "@mio/helpers";

export const teamEventFactory = {
  createSingle: (
    props: Partial<TeamEvent.CreateSingleDto> & {
      teamId: Team["id"];
      organizationId: Organization["id"];
      invitations: Player["id"][];
    },
    now = new Date(),
  ): TeamEvent.SingleTeamEvent => {
    return TeamEvent.Entity.createSingle({
      ...({
        name: "Test Event",
        agenda: TeamEvent.Agenda.Match,
        description: "Test event description",
        location: {
          address: "Test address",
          postcode: "12345",
        },
        startDateTime: addDays(now, 1),
        endDateTime: addDays(now, 1),
      } as TeamEvent.CreateSingleDto),
      ...props,
    });
  },

  createRecurringParent: (props: {
    teamId: Team["id"];
    organizationId: Organization["id"];
    invitations: Player["id"][];
    hosts: Profile["id"][];
    agenda?: TeamEvent.Agenda;
    startFrom?: Date;
    endDate?: Date;
    day?: EventScheduling.DayOfWeek;
  }): RecurringTeamEvent.RecurringTeamEvent => {
    const startFrom = (props.startFrom ?? addDays(new Date(), 1)) as Date;
    const endDate = (props.endDate ?? addDays(startFrom, 14)) as Date;
    const day =
      props.day ?? (CustomDate.getDayOfWeek(startFrom as any) as EventScheduling.DayOfWeek);

    const dto: RecurringTeamEvent.CreateDto = {
      organizationId: props.organizationId,
      teamId: props.teamId,
      status: RecurringTeamEvent.Status.Active,
      agenda: (props.agenda ?? TeamEvent.Agenda.TrainingSession) as any,
      name: "Recurring Event" as StringOfLength<2, 100>,
      description: "Test recurring event" as StringOfLength<2, 2000>,
      location: {
        address: "Test address",
        postcode: "12345",
      } as RecurringTeamEvent.CreateDto["location"],
      hosts: props.hosts,
      invitations: props.invitations,
      schedule: {
        type: EventScheduling.Type.Weekly,
        day,
        duration: {
          type: EventScheduling.EndCriteria.CustomDate,
          date: endDate as ValidDate,
        },
        startTime: CustomDate.timeNow(),
        endTime: CustomDate.timeAddMinutes(60),
        startFrom: startFrom as ValidDate,
      },
    };

    return RecurringTeamEvent.Entity.create(dto);
  },

  createRecurringInstance: (
    parent: RecurringTeamEvent.RecurringTeamEvent,
    date: Date,
  ): TeamEvent.RecurringTeamEvent => {
    return TeamEvent.Entity.createRecurring(parent, date as any);
  },
};
