import { produce } from "immer";
import { merge, sample } from "lodash";
import { faker } from "@faker-js/faker";

import {
  CreatePlayerDto,
  Organization,
  Prettify,
  Primitive,
  Player,
  CustomDate,
  PlayerGenders,
  PlayingExperience,
  must,
} from "@mio/helpers";

type SimplifiedCreatePlayerDto = Prettify<Primitive<CreatePlayerDto>>;

const generatePlayingExperienceDescription = (): string => {
  const years = faker.datatype.number({ min: 1, max: 10 });
  return `Played for ${years} year(s) in a local league.`;
};

const generateMedicalConditions = (): string => {
  return sample([
    "None",
    "Asthma",
    "Knee injury",
    "Allergy to pollen",
    "Requires inhaler during physical activity",
  ]);
};

const createNew = (
  organizationId: Organization["id"],
  type: "youth" | "default" | "adult" = "default",
  overrides: Partial<SimplifiedCreatePlayerDto> = {},
): Player => {
  switch (type) {
    case "youth":
      return createYouthPlayer(organizationId, overrides);
    case "adult":
      return createAdultPlayer(organizationId, overrides);
    default:
      return createDefaultPlayer(organizationId, overrides);
  }
};

// Create a player eligible for youth team (Under 12s)
const createYouthPlayer = (
  organizationId: Organization["id"],
  overrides: Partial<SimplifiedCreatePlayerDto> = {},
): Player => {
  const genderName = must(sample(Object.values(PlayerGenders)));
  const defaultData: SimplifiedCreatePlayerDto = {
    organizationId,
    firstName: faker.name.firstName(),
    lastName: faker.name.lastName(),
    email: faker.internet.email(),
    phone: faker.phone.number(),
    dob: CustomDate.subYears(11), // Between 11-12 years old
    gender: {
      name: genderName,
      ...(genderName === "other" ? { description: "Other gender description" } : {}),
    },
    playingExperience: must(sample(Object.values(PlayingExperience))),
    playingExperienceDescription: generatePlayingExperienceDescription(),
    medicalConditions: generateMedicalConditions(),
    address: {
      postcode: faker.address.zipCode(),
      address: `${faker.address.streetAddress()}, ${faker.address.city()}, ${faker.address.country()}`,
    },
    acceptedTerms: true,
  };

  const dto = Player.toCreateDtoOrThrow(
    produce(defaultData, (draft) => {
      merge(draft, overrides);
    }),
  );

  return Player.createNewPlayer(dto);
};

// Create a player eligible for default team (Under 16s)
const createDefaultPlayer = (
  organizationId: Organization["id"],
  overrides: Partial<SimplifiedCreatePlayerDto> = {},
): Player => {
  const genderName = must(sample(Object.values(PlayerGenders)));
  const defaultData: SimplifiedCreatePlayerDto = {
    organizationId,
    firstName: faker.name.firstName(),
    lastName: faker.name.lastName(),
    email: faker.internet.email(),
    phone: faker.phone.number(),
    dob: CustomDate.subYears(15), // Between 15-16 years old
    gender: {
      name: genderName,
      ...(genderName === "other" ? { description: "Other gender description" } : {}),
    },
    playingExperience: must(sample(Object.values(PlayingExperience))),
    playingExperienceDescription: generatePlayingExperienceDescription(),
    medicalConditions: generateMedicalConditions(),
    address: {
      postcode: faker.address.zipCode(),
      address: `${faker.address.streetAddress()}, ${faker.address.city()}, ${faker.address.country()}`,
    },
    acceptedTerms: true,
  };

  const dto = Player.toCreateDtoOrThrow(
    produce(defaultData, (draft) => {
      merge(draft, overrides);
    }),
  );

  return Player.createNewPlayer(dto);
};

// Create a player eligible for adult team (18+ years old)
const createAdultPlayer = (
  organizationId: Organization["id"],
  overrides: Partial<SimplifiedCreatePlayerDto> = {},
): Player => {
  const genderName = must(sample(Object.values(PlayerGenders)));
  const defaultData: SimplifiedCreatePlayerDto = {
    organizationId,
    firstName: faker.name.firstName(),
    lastName: faker.name.lastName(),
    email: faker.internet.email(),
    phone: faker.phone.number(),
    dob: CustomDate.subYears(25), // 25 years old (18+ years old)
    gender: {
      name: genderName,
      ...(genderName === "other" ? { description: "Other gender description" } : {}),
    },
    playingExperience: must(sample(Object.values(PlayingExperience))),
    playingExperienceDescription: generatePlayingExperienceDescription(),
    medicalConditions: generateMedicalConditions(),
    address: {
      postcode: faker.address.zipCode(),
      address: `${faker.address.streetAddress()}, ${faker.address.city()}, ${faker.address.country()}`,
    },
    acceptedTerms: true,
  };

  const dto = Player.toCreateDtoOrThrow(
    produce(defaultData, (draft) => {
      merge(draft, overrides);
    }),
  );

  return Player.createNewPlayer(dto);
};

export const playerFactory = {
  create: createNew,
  createYouth: createYouthPlayer,
  createDefault: createDefaultPlayer,
  createAdult: createAdultPlayer,
};
