import { Inject, Injectable, Logger } from "@nestjs/common";

import {
  PlayerUser,
  CustomDate,
  DomainError,
  Email,
  ErrorMessages,
  isError,
  ParsingError,
  Primitive,
  UnexpectedError,
  WithVersioning,
  LoginCodeDto,
} from "@mio/helpers";

import { DATABASE_CONNECTION, DBClient, DBCollection } from "../database";

type UserDocument = Primitive<PlayerUser.PlayerUser> & Partial<WithVersioning>;

@Injectable()
export class PlayerUsersRepository {
  private collection: DBCollection<UserDocument>;
  private readonly logger = new Logger(PlayerUsersRepository.name);

  constructor(@Inject(DATABASE_CONNECTION) private db: DBClient) {
    this.collection = db.collection("player-users");
    this.createIndexes();
  }

  private async createIndexes() {
    try {
      await this.collection.createIndex({ id: 1 }, { unique: true });
      await this.collection.createIndex({ "authentication.email": 1 });
      await this.collection.createIndex({ "authentication.code": 1 });
    } catch (err) {
      this.logger.error(`Error creating indexes for ${this.constructor.name}`, err);
    }
  }

  async findByCode(
    dto: LoginCodeDto,
  ): Promise<UnexpectedError | ParsingError | PlayerUser.PlayerUser[]> {
    return this.collection
      .find({
        "authentication.code": dto.code,
      })
      .toArray()
      .then((result) => {
        if (result) {
          const parsed = PlayerUser.Entity.toEntities(result);

          if (isError(parsed)) {
            parsed.addContext({
              service: PlayerUsersRepository.name,
              method: this.findByCode.name,
              operation: PlayerUser.Entity.toEntities.name,
              payload: result,
            });
          }

          return parsed;
        }

        return [];
      })
      .catch((err) => {
        return new UnexpectedError(err, {
          service: PlayerUsersRepository.name,
          method: this.findByCode.name,
          operation: this.collection.findOne.name,
        });
      });
  }

  async getByEmail(
    email: Email,
  ): Promise<UnexpectedError | ParsingError | PlayerUser.PlayerUser | null> {
    return this.collection
      .findOne({
        "authentication.email": email,
      })
      .then((result) => {
        if (result) {
          const parsed = PlayerUser.Entity.toEntity(result);

          if (isError(parsed)) {
            parsed.addContext({
              service: PlayerUsersRepository.name,
              method: this.getByEmail.name,
              operation: PlayerUser.Entity.toEntity.name,
              payload: result,
            });
          }

          return parsed;
        }

        return null;
      })
      .catch((err) => {
        return new UnexpectedError(err, {
          service: PlayerUsersRepository.name,
          method: this.getByEmail.name,
          operation: this.collection.findOne.name,
        });
      });
  }

  async getUserById(
    id: PlayerUser.PlayerUserId,
  ): Promise<UnexpectedError | ParsingError | PlayerUser.PlayerUser | null> {
    return this.collection
      .findOne({
        id,
      })
      .then((response) => {
        if (response) {
          const parsed = PlayerUser.Entity.toEntity(response);

          if (isError(parsed)) {
            parsed.addContext({
              service: PlayerUsersRepository.name,
              method: this.getUserById.name,
              operation: PlayerUser.Entity.toEntity.name,
              payload: response,
            });
          }

          return parsed;
        }
        return null;
      })
      .catch((err) => {
        return new UnexpectedError(err, {
          service: PlayerUsersRepository.name,
          method: this.getUserById.name,
          operation: this.collection.findOne.name,
        });
      });
  }

  async update(user: PlayerUser.PlayerUser): Promise<UnexpectedError | DomainError | void> {
    return this.collection
      .replaceOne({ id: user.id }, user)
      .then((res) => {
        if (res.modifiedCount < 1) {
          return new DomainError(ErrorMessages.EntityNotFound, {
            service: PlayerUsersRepository.name,
            method: this.update.name,
            operation: this.collection.replaceOne.name,
            user,
          });
        }
        if (res.modifiedCount > 1) {
          return new DomainError(ErrorMessages.MultipleEntitiesFound, {
            service: PlayerUsersRepository.name,
            method: this.update.name,
            operation: this.collection.replaceOne.name,
            user,
          });
        }

        return undefined;
      })
      .catch((err) => {
        return new UnexpectedError(err, {
          service: PlayerUsersRepository.name,
          method: this.update.name,
          operation: this.collection.replaceOne.name,
          user,
        });
      });
  }

  async create(user: PlayerUser.PlayerUser): Promise<UnexpectedError | void> {
    return this.collection
      .insertOne({ ...user, createdAt: CustomDate.now() })
      .then(() => undefined)
      .catch((err) => {
        return new UnexpectedError(err, {
          service: PlayerUsersRepository.name,
          method: this.create.name,
          operation: this.collection.insertOne.name,
          user,
        });
      });
  }
}
