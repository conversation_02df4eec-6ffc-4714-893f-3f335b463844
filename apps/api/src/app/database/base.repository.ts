import { Inject, Injectable, Logger } from "@nestjs/common";
import { ClientSession, MongoClient } from "mongodb";

import { DATABASE_CLIENT, DATABASE_CONNECTION, DBClient } from "./index";
import { UnexpectedError, isError } from "@mio/helpers";

@Injectable()
export class BaseRepository {
  protected db: DBClient;
  protected mongoClient: MongoClient;
  protected readonly logger: Logger;

  constructor(
    @Inject(DATABASE_CONNECTION) db: DBClient,
    @Inject(DATABASE_CLIENT) mongoClient: MongoClient,
  ) {
    this.db = db;
    this.mongoClient = mongoClient;
    this.logger = new Logger(this.constructor.name);
  }

  protected getDb(): DBClient {
    return this.db;
  }

  protected getMongoClient(): MongoClient {
    return this.mongoClient;
  }

  public startSession(): ClientSession {
    return this.mongoClient.startSession();
  }

  /**
   * Execute operations in a transaction
   * @param operationFn Function that contains the operations to execute in the transaction
   * @returns Result of the operation function
   */
  public async executeTransaction<T>(
    operationFn: (session: ClientSession) => Promise<T>,
  ): Promise<T> {
    const session = this.startSession();
    let result: T;

    try {
      session.startTransaction();
      result = await operationFn(session);

      // If the operation returned a CustomError-like value instead of throwing, treat it as failure
      if (isError(result)) {
        throw result.addContext({
          service: BaseRepository.name,
          method: this.executeTransaction.name,
          operation: operationFn.name,
          message: "Operation returned error instance instead of throwing",
        });
      }
      await session.commitTransaction();

      return result;
    } catch (error) {
      try {
        await session.abortTransaction();
      } catch (abortError) {
        // Log abort failure but do not mask the original error
        this.logger.error("Failed to abort transaction:", abortError);
      }
      // Re-throw the original error enriched with context
      throw isError(error)
        ? error.addContext({
            service: BaseRepository.name,
            method: this.executeTransaction.name,
            operation: "executeTransaction",
            message: "Transaction failed",
          })
        : new UnexpectedError(error, {
            service: BaseRepository.name,
            method: this.executeTransaction.name,
            operation: "executeTransaction",
            message: "Transaction failed",
          });
    } finally {
      try {
        await session.endSession();
      } catch (endError) {
        this.logger.error("Failed to end session:", endError);
      }
    }
  }
}
