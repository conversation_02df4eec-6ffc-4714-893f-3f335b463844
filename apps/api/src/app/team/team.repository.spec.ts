import { Test, TestingModule } from "@nestjs/testing";

import {
  Team,
  ParsingError,
  UnexpectedError,
  UUID,
  CustomDate,
  PositiveInteger,
} from "@mio/helpers";
import { DATABASE_CONNECTION } from "../database";
import { TeamRepository } from "./team.repository";

describe(TeamRepository.name, () => {
  let repository: TeamRepository;

  describe(TeamRepository.prototype.createTeam.name, () => {
    const validTeam = Team.parse({
      name: "<PERSON><PERSON><PERSON>",
      id: UUID.generate(),
      organizationId: UUID.generate(),
      playersBornAfter: CustomDate.subDays(3000 as PositiveInteger),
      playersBornBefore: CustomDate.subDays(2500 as PositiveInteger),
      ageDescription: "Under 15s",
      gender: "male",
    });

    it("returns undefined on success", async () => {
      const module: TestingModule = await Test.createTestingModule({
        providers: [
          TeamRepository,
          {
            provide: DATABASE_CONNECTION,
            useValue: {
              collection: () => ({
                insertOne: async () => undefined,
                createIndex: async () => undefined,
              }),
            },
          },
        ],
      }).compile();

      repository = module.get<TeamRepository>(TeamRepository);

      const result = await repository.createTeam(validTeam);
      expect(result).toBeUndefined();
    });

    it("returns an UnexpectedError", async () => {
      const module: TestingModule = await Test.createTestingModule({
        providers: [
          TeamRepository,
          {
            provide: DATABASE_CONNECTION,
            useValue: {
              collection: () => ({
                insertOne: async () => Promise.reject(new Error("something went wrong")),
                createIndex: async () => undefined,
              }),
            },
          },
        ],
      }).compile();

      repository = module.get<TeamRepository>(TeamRepository);

      const result = await repository.createTeam(validTeam);
      expect(result).toBeInstanceOf(UnexpectedError);
    });
  });

  describe(TeamRepository.prototype.getById.name, () => {
    const validTeam = Team.parse({
      name: "Krakra",
      id: UUID.generate(),
      organizationId: UUID.generate(),
      playersBornAfter: CustomDate.subDays(3000 as PositiveInteger),
      playersBornBefore: CustomDate.subDays(2500 as PositiveInteger),
      ageDescription: "Under 15s",
      gender: "male",
    });

    it("returns the Team", async () => {
      const module: TestingModule = await Test.createTestingModule({
        providers: [
          TeamRepository,
          {
            provide: DATABASE_CONNECTION,
            useValue: {
              collection: () => ({
                findOne: async () => validTeam,
                createIndex: async () => undefined,
              }),
            },
          },
        ],
      }).compile();

      repository = module.get<TeamRepository>(TeamRepository);

      const result = await repository.getById(validTeam.id);
      expect(result).toEqual(validTeam);
    });

    it("returns a ParsingError", async () => {
      const module: TestingModule = await Test.createTestingModule({
        providers: [
          TeamRepository,
          {
            provide: DATABASE_CONNECTION,
            useValue: {
              collection: () => ({
                findOne: async () => ({ id: "not a valid id" }),
                createIndex: async () => undefined,
              }),
            },
          },
        ],
      }).compile();

      repository = module.get<TeamRepository>(TeamRepository);

      const result = await repository.getById(validTeam.id);
      expect(result).toBeInstanceOf(ParsingError);
    });

    it("returns null when the Team is not found", async () => {
      const module: TestingModule = await Test.createTestingModule({
        providers: [
          TeamRepository,
          {
            provide: DATABASE_CONNECTION,
            useValue: {
              collection: () => ({
                findOne: async () => null,
                createIndex: async () => undefined,
              }),
            },
          },
        ],
      }).compile();

      repository = module.get<TeamRepository>(TeamRepository);

      const result = await repository.getById(validTeam.id);
      expect(result).toBeNull();
    });

    it("handles unexpected errors", async () => {
      const module: TestingModule = await Test.createTestingModule({
        providers: [
          TeamRepository,
          {
            provide: DATABASE_CONNECTION,
            useValue: {
              collection: () => ({
                findOne: async () => Promise.reject(new Error("oops")),
                createIndex: async () => undefined,
              }),
            },
          },
        ],
      }).compile();

      repository = module.get<TeamRepository>(TeamRepository);

      const result = await repository.getById(validTeam.id);
      expect(result).toBeInstanceOf(UnexpectedError);
    });
  });
});
