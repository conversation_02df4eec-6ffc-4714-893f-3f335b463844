import { Inject, Injectable, Logger } from "@nestjs/common";

import {
  Primitive,
  UnexpectedError,
  TeamId,
  ParsingError,
  isError,
  DomainError,
  ErrorMessages,
  TrainingSessionPlan,
} from "@mio/helpers";

import { DATABASE_CONNECTION, DBClient, DBCollection } from "../../database";

type TrainingSessionPlanDocument = Primitive<TrainingSessionPlan.Plan>;

export const trainingSessionPlanCollection = "training-session-plans";

@Injectable()
export class TrainingSessionPlanRepository {
  private collection: DBCollection<TrainingSessionPlanDocument>;
  private readonly logger = new Logger(TrainingSessionPlanRepository.name);

  constructor(@Inject(DATABASE_CONNECTION) private db: DBClient) {
    this.collection = db.collection(trainingSessionPlanCollection);
    this.createIndexes();
  }

  private async createIndexes() {
    try {
      await this.collection.createIndex({ id: 1 }, { unique: true });
      await this.collection.createIndex({ teamId: 1 });
    } catch (err) {
      this.logger.error(`Error creating indexes for ${this.constructor.name}`, err);
    }
  }

  //#region Commands

  async create(plan: TrainingSessionPlan.Plan): Promise<UnexpectedError | void> {
    return this.collection
      .insertOne({
        ...plan,
      })
      .then(() => undefined)
      .catch((err) => {
        // TODO: log
        return new UnexpectedError(err, {
          service: TrainingSessionPlanRepository.name,
          method: this.create.name,
          operation: this.collection.insertOne.name,
        });
      });
  }

  async update(plan: TrainingSessionPlan.Plan) {
    const result = await this.collection
      .replaceOne({ id: plan.id }, plan)
      .then((res) => {
        if (res.modifiedCount < 1) {
          return new DomainError(ErrorMessages.EntityNotFound, {
            service: TrainingSessionPlanRepository.name,
            method: this.update.name,
            operation: this.collection.replaceOne.name,
            plan: plan,
          });
        }
        if (res.modifiedCount > 1) {
          return new DomainError(ErrorMessages.MultipleEntitiesFound, {
            service: TrainingSessionPlanRepository.name,
            method: this.update.name,
            operation: this.collection.replaceOne.name,
            plan: plan,
          });
        }

        return undefined;
      })
      .catch((err) => {
        return new UnexpectedError(err, {
          service: TrainingSessionPlanRepository.name,
          method: this.update.name,
          operation: this.collection.replaceOne.name,
          plan: plan,
        });
      });

    if (isError(result)) {
      result.addContext({
        service: TrainingSessionPlanRepository.name,
        method: this.update.name,
        operation: this.collection.replaceOne,
        payload: result,
      });

      return result;
    }

    return undefined;
  }

  //#endregion

  //#region Queries

  async getById(
    id: TrainingSessionPlan.PlanId,
  ): Promise<UnexpectedError | ParsingError | TrainingSessionPlan.Plan | null> {
    return this.collection
      .findOne({ id })
      .then((plan) => {
        if (plan) {
          const parsed = TrainingSessionPlan.Entity.toEntity(plan);
          if (isError(parsed)) {
            parsed.addContext({
              service: TrainingSessionPlanRepository.name,
              method: this.getById.name,
              operation: TrainingSessionPlan.Entity.toEntity.name,
              planId: id,
            });

            return parsed;
          }

          return parsed;
        }
        return null;
      })
      .catch((err) => {
        // TODO: log
        return new UnexpectedError(err, {
          service: TrainingSessionPlanRepository.name,
          method: this.getById.name,
          operation: this.collection.findOne.name,
        });
      });
  }

  getByTeamId(
    teamId: TeamId,
  ): Promise<UnexpectedError | ParsingError | TrainingSessionPlan.Plan[]> {
    return this.collection
      .find({ teamId })
      .toArray()
      .then((plans) => {
        if (plans) {
          const parsed = TrainingSessionPlan.Entity.toEntities(plans);
          if (isError(parsed)) {
            parsed.addContext({
              service: TrainingSessionPlanRepository.name,
              method: this.getByTeamId.name,
              operation: TrainingSessionPlan.Entity.toEntities.name,
              teamId,
            });

            return parsed;
          }

          return parsed;
        }

        return [];
      })
      .catch((err) => {
        // TODO: log
        return new UnexpectedError(err, {
          service: TrainingSessionPlanRepository.name,
          method: this.getByTeamId.name,
          operation: this.collection.find.name,
        });
      });
  }

  //#endregion
}
