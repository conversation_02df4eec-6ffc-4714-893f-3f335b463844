import { Inject, Injectable, Logger } from "@nestjs/common";

import {
  Primitive,
  UnexpectedError,
  TeamId,
  ParsingError,
  isError,
  DomainError,
  ErrorMessages,
  TrainingSessionPlayerReview,
} from "@mio/helpers";

import { DATABASE_CONNECTION, DBClient, DBCollection } from "../../database";
import { teamEventsCollection } from "../../team-events/instances/team-event.repository";
import { playersCollection } from "../../player/player.repository";
import { trainingSessionReviewCollection } from "../training-session-review/training-session-review.repository";

type TrainingSessionPlayerReviewDocument = Primitive<TrainingSessionPlayerReview.PlayerReview>;

export const trainingSessionPlayerReviewCollection = "training-session-player-reviews";

@Injectable()
export class TrainingSessionPlayerReviewRepository {
  private collection: DBCollection<TrainingSessionPlayerReviewDocument>;
  private readonly logger = new Logger(TrainingSessionPlayerReviewRepository.name);

  constructor(@Inject(DATABASE_CONNECTION) private db: DBClient) {
    this.collection = db.collection(trainingSessionPlayerReviewCollection);
    this.createIndexes();
  }

  private async createIndexes() {
    try {
      await this.collection.createIndex({ id: 1 }, { unique: true });
      await this.collection.createIndex({ teamId: 1 });
    } catch (err) {
      this.logger.error(`Error creating indexes for ${this.constructor.name}`, err);
    }
  }

  //#region Commands

  async create(
    playerReview: TrainingSessionPlayerReview.PlayerReview,
  ): Promise<UnexpectedError | void> {
    return this.collection
      .insertOne({
        ...playerReview,
      })
      .then(() => undefined)
      .catch((err) => {
        // TODO: log
        return new UnexpectedError(err, {
          service: TrainingSessionPlayerReviewRepository.name,
          method: this.create.name,
          operation: this.collection.insertOne.name,
        });
      });
  }

  async update(playerReview: TrainingSessionPlayerReview.PlayerReview) {
    const result = await this.collection
      .replaceOne({ id: playerReview.id }, playerReview)
      .then((res) => {
        if (res.modifiedCount < 1) {
          return new DomainError(ErrorMessages.EntityNotFound, {
            service: TrainingSessionPlayerReviewRepository.name,
            method: this.update.name,
            operation: this.collection.replaceOne.name,
            playerReview,
          });
        }
        if (res.modifiedCount > 1) {
          return new DomainError(ErrorMessages.MultipleEntitiesFound, {
            service: TrainingSessionPlayerReviewRepository.name,
            method: this.update.name,
            operation: this.collection.replaceOne.name,
            playerReview,
          });
        }

        return undefined;
      })
      .catch((err) => {
        return new UnexpectedError(err, {
          service: TrainingSessionPlayerReviewRepository.name,
          method: this.update.name,
          operation: this.collection.replaceOne.name,
          playerReview,
        });
      });

    if (isError(result)) {
      result.addContext({
        service: TrainingSessionPlayerReviewRepository.name,
        method: this.update.name,
        operation: this.collection.replaceOne,
        payload: result,
      });

      return result;
    }

    return undefined;
  }

  //#endregion

  //#region Queries

  async getExtendedReviews(
    teamId: TeamId,
    query: TrainingSessionPlayerReview.QueryDto,
  ): Promise<UnexpectedError | ParsingError | TrainingSessionPlayerReview.ExtendedReview[]> {
    const stages = [
      {
        $match: { teamId },
      },
      {
        $lookup: {
          from: trainingSessionReviewCollection,
          localField: "trainingSessionReviewId",
          foreignField: "id",
          as: "parentReview",
        },
      },
      {
        $unwind: "$parentReview",
      },
      {
        $lookup: {
          from: teamEventsCollection,
          localField: "parentReview.teamEventId",
          foreignField: "id",
          as: "teamEvent",
        },
      },
      {
        $unwind: "$teamEvent",
      },
      {
        $match: { "teamEvent.startDateTime": { $gte: query.startDate, $lte: query.endDate } },
      },
      {
        $lookup: {
          from: playersCollection,
          localField: "playerId",
          foreignField: "id",
          as: "player",
        },
      },
      {
        $unwind: "$player",
      },
    ];

    return this.collection
      .aggregate(stages)
      .toArray()
      .then((result) => {
        const parsed = TrainingSessionPlayerReview.Entity.toExtendedEntities(result);

        if (isError(parsed)) {
          parsed.addContext({
            service: TrainingSessionPlayerReviewRepository.name,
            method: this.getExtendedReviews.name,
            operation: TrainingSessionPlayerReview.Entity.toExtendedEntities.name,
            data: result,
            query,
          });

          return parsed;
        }

        return parsed;
      })
      .catch((err) => {
        return new UnexpectedError(err, {
          service: TrainingSessionPlayerReviewRepository.name,
          method: this.getExtendedReviews.name,
          query,
        });
      });
  }

  async getById(
    id: TrainingSessionPlayerReview.PlayerReviewId,
  ): Promise<UnexpectedError | ParsingError | TrainingSessionPlayerReview.PlayerReview | null> {
    return this.collection
      .findOne({ id })
      .then((playerReview) => {
        if (playerReview) {
          const parsed = TrainingSessionPlayerReview.Entity.toEntity(playerReview);
          if (isError(parsed)) {
            parsed.addContext({
              service: TrainingSessionPlayerReviewRepository.name,
              method: this.getById.name,
              operation: TrainingSessionPlayerReview.Entity.toEntity.name,
              playerReviewId: id,
            });

            return parsed;
          }

          return parsed;
        }
        return null;
      })
      .catch((err) => {
        // TODO: log
        return new UnexpectedError(err, {
          service: TrainingSessionPlayerReviewRepository.name,
          method: this.getById.name,
          operation: this.collection.findOne.name,
        });
      });
  }

  async getByTeamId(
    teamId: TeamId,
  ): Promise<UnexpectedError | ParsingError | TrainingSessionPlayerReview.PlayerReview[]> {
    return this.collection
      .find({ teamId })
      .toArray()
      .then((playerReviews) => {
        if (playerReviews) {
          const parsed = TrainingSessionPlayerReview.Entity.toEntities(playerReviews);
          if (isError(parsed)) {
            parsed.addContext({
              service: TrainingSessionPlayerReviewRepository.name,
              method: this.getByTeamId.name,
              operation: TrainingSessionPlayerReview.Entity.toEntities.name,
              teamId,
            });

            return parsed;
          }

          return parsed;
        }

        return [];
      })
      .catch((err) => {
        // TODO: log
        return new UnexpectedError(err, {
          service: TrainingSessionPlayerReviewRepository.name,
          method: this.getByTeamId.name,
          operation: this.collection.find.name,
        });
      });
  }

  //#endregion
}
