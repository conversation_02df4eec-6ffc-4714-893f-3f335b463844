import { Inject, Injectable, Logger } from "@nestjs/common";

import {
  Primitive,
  UnexpectedError,
  TeamId,
  ParsingError,
  isError,
  DomainError,
  ErrorMessages,
  TrainingSessionPractice,
} from "@mio/helpers";

import { DATABASE_CONNECTION, DBClient, DBCollection } from "../../database";

type TrainingSessionPracticeDocument = Primitive<TrainingSessionPractice.Practice>;

export const trainingSessionPracticeCollection = "training-session-practices";

@Injectable()
export class TrainingSessionPracticeRepository {
  private collection: DBCollection<TrainingSessionPracticeDocument>;
  private readonly logger = new Logger(TrainingSessionPracticeRepository.name);

  constructor(@Inject(DATABASE_CONNECTION) private db: DBClient) {
    this.collection = db.collection(trainingSessionPracticeCollection);
    this.createIndexes();
  }

  private async createIndexes() {
    try {
      await this.collection.createIndex({ id: 1 }, { unique: true });
      await this.collection.createIndex({ teamId: 1 });
    } catch (err) {
      this.logger.error(`Error creating indexes for ${this.constructor.name}`, err);
    }
  }

  //#region Commands

  async create(practice: TrainingSessionPractice.Practice): Promise<UnexpectedError | void> {
    return this.collection
      .insertOne({
        ...practice,
      })
      .then(() => undefined)
      .catch((err) => {
        // TODO: log
        return new UnexpectedError(err, {
          service: TrainingSessionPracticeRepository.name,
          method: this.create.name,
          operation: this.collection.insertOne.name,
        });
      });
  }

  async update(practice: TrainingSessionPractice.Practice) {
    const result = await this.collection
      .replaceOne({ id: practice.id }, practice)
      .then((res) => {
        if (res.modifiedCount < 1) {
          return new DomainError(ErrorMessages.EntityNotFound, {
            service: TrainingSessionPracticeRepository.name,
            method: this.update.name,
            operation: this.collection.replaceOne.name,
            practice,
          });
        }
        if (res.modifiedCount > 1) {
          return new DomainError(ErrorMessages.MultipleEntitiesFound, {
            service: TrainingSessionPracticeRepository.name,
            method: this.update.name,
            operation: this.collection.replaceOne.name,
            practice,
          });
        }

        return undefined;
      })
      .catch((err) => {
        return new UnexpectedError(err, {
          service: TrainingSessionPracticeRepository.name,
          method: this.update.name,
          operation: this.collection.replaceOne.name,
          practice: practice,
        });
      });

    if (isError(result)) {
      result.addContext({
        service: TrainingSessionPracticeRepository.name,
        method: this.update.name,
        operation: this.collection.replaceOne,
        payload: result,
      });

      return result;
    }

    return undefined;
  }

  //#endregion

  //#region Queries

  async getById(
    id: TrainingSessionPractice.PracticeId,
  ): Promise<UnexpectedError | ParsingError | TrainingSessionPractice.Practice | null> {
    return this.collection
      .findOne({ id })
      .then((practice) => {
        if (practice) {
          const parsed = TrainingSessionPractice.Entity.toEntity(practice);
          if (isError(parsed)) {
            parsed.addContext({
              service: TrainingSessionPracticeRepository.name,
              method: this.getById.name,
              operation: TrainingSessionPractice.Entity.toEntity.name,
              practiceId: id,
            });

            return parsed;
          }

          return parsed;
        }
        return null;
      })
      .catch((err) => {
        // TODO: log
        return new UnexpectedError(err, {
          service: TrainingSessionPracticeRepository.name,
          method: this.getById.name,
          operation: this.collection.findOne.name,
        });
      });
  }

  getByTeamId(
    teamId: TeamId,
  ): Promise<UnexpectedError | ParsingError | TrainingSessionPractice.Practice[]> {
    return this.collection
      .find({ teamId })
      .toArray()
      .then((practices) => {
        if (practices) {
          const parsed = TrainingSessionPractice.Entity.toEntities(practices);
          if (isError(parsed)) {
            parsed.addContext({
              service: TrainingSessionPracticeRepository.name,
              method: this.getByTeamId.name,
              operation: TrainingSessionPractice.Entity.toEntities.name,
              teamId,
            });

            return parsed;
          }

          return parsed;
        }

        return [];
      })
      .catch((err) => {
        // TODO: log
        return new UnexpectedError(err, {
          service: TrainingSessionPracticeRepository.name,
          method: this.getByTeamId.name,
          operation: this.collection.find.name,
        });
      });
  }

  //#endregion
}
