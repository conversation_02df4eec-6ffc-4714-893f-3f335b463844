import { Inject, Injectable, Logger } from "@nestjs/common";

import {
  Primitive,
  UnexpectedError,
  TeamId,
  ParsingError,
  isError,
  OrganizationId,
  PlayerTeamProfile,
  PlayerTeamStatus,
  PlayerId,
  PlayerTeamProfileId,
  DomainError,
  ErrorMessages,
  AssignedPlayerTeamProfile,
  FootballMatchStats,
  StatIndividualRole,
} from "@mio/helpers";

import { DATABASE_CONNECTION, DBClient, DBCollection } from "../database";

type PlayerTeamProfileDocument = Primitive<PlayerTeamProfile | AssignedPlayerTeamProfile>;

export const playerTeamProfileCollection = "player-team-profiles";

@Injectable()
export class PlayerTeamProfileRepository {
  private collection: DBCollection<PlayerTeamProfileDocument>;
  private readonly logger = new Logger(PlayerTeamProfileRepository.name);

  constructor(@Inject(DATABASE_CONNECTION) private db: DBClient) {
    this.collection = db.collection(playerTeamProfileCollection);
    this.createIndexes();
  }

  private async createIndexes() {
    try {
      await this.collection.createIndex({ id: 1 }, { unique: true });
      await this.collection.createIndex({ organizationId: 1, playerId: 1, status: 1 });
      await this.collection.createIndex({ organizationId: 1, teamId: 1, status: 1 });
    } catch (err) {
      this.logger.error(`Error creating indexes for ${this.constructor.name}`, err);
    }
  }

  //#region Commands

  async createPlayerTeamProfile(
    playerTeamProfile: PlayerTeamProfile,
  ): Promise<UnexpectedError | void> {
    return this.collection
      .insertOne({
        ...playerTeamProfile,
      })
      .then(() => undefined)
      .catch((err) => {
        // TODO: log
        return new UnexpectedError(err);
      });
  }

  async updatePersonalInfo(profile: PlayerTeamProfile) {
    return this.collection
      .updateOne(
        { id: profile.id },
        {
          $set: {
            firstName: profile.firstName,
            lastName: profile.lastName,
            email: profile.email,
            phone: profile.phone,
            dob: profile.dob,
            medicalConditions: profile.medicalConditions,
            gender: profile.gender,
            playingExperience: profile.playingExperience,
            playingExperienceDescription: profile.playingExperienceDescription,
            address: profile.address,
            guardian: profile.guardian,
            documents: profile.documents,
          },
        },
      )
      .catch((err) => {
        // TODO: log
        return new UnexpectedError(err);
      });
  }

  async updateToRemovedFromOrganization(playerId: PlayerId, organizationId: OrganizationId) {
    return this.collection.updateMany(
      { playerId, organizationId },
      { $set: { status: PlayerTeamStatus.RemovedOrganization } },
    );
  }

  async replace(profile: PlayerTeamProfile) {
    const result = await this.collection.replaceOne({ id: profile.id }, profile).catch((err) => {
      // TODO: log
      return new UnexpectedError(err);
    });

    if (isError(result)) {
      return result;
    }

    if (result.matchedCount < 1) {
      return new DomainError(ErrorMessages.EntityNotFound);
    }

    if (result.matchedCount > 1) {
      return new DomainError(ErrorMessages.MultipleEntitiesFound);
    }

    return undefined;
  }

  async delete(playerTeamProfileIds: PlayerTeamProfileId[]) {
    return this.collection
      .deleteMany({
        id: playerTeamProfileIds,
      })
      .then(() => undefined)
      .catch((err) => {
        // TODO: log
        return new UnexpectedError(err);
      });
  }

  //#endregion

  //#region Queries

  async getById(
    id: PlayerTeamProfileId,
  ): Promise<UnexpectedError | ParsingError | PlayerTeamProfile | null> {
    return this.collection
      .findOne({ id, status: { $ne: PlayerTeamStatus.RemovedOrganization } })
      .then((playerTeamProfile) => {
        if (playerTeamProfile) {
          const asEntity = PlayerTeamProfile.toEntity(playerTeamProfile);
          if (isError(asEntity)) {
            // TODO: log
          }

          return asEntity;
        }
        return null;
      })
      .catch((err) => {
        // TODO: log
        return new UnexpectedError(err);
      });
  }

  async getByTeamId(teamId: TeamId): Promise<UnexpectedError | ParsingError | PlayerTeamProfile[]> {
    return this.collection
      .find({ teamId, status: { $ne: PlayerTeamStatus.RemovedOrganization } })
      .toArray()
      .then((player) => {
        if (player) {
          const asEntity = PlayerTeamProfile.toManyEntities(player);
          if (isError(asEntity)) {
            // TODO: log
          }

          return asEntity;
        }
        return [];
      })
      .catch((err) => {
        // TODO: log
        return new UnexpectedError(err);
      });
  }

  buildGetByOrganizationMongoQuery(
    organizationId: OrganizationId,
    teamId?: TeamId,
    playerStatus?: PlayerTeamStatus,
  ) {
    return {
      organizationId,
      ...(teamId && { teamId }),
      ...(playerStatus && {
        status: { $ne: PlayerTeamStatus.RemovedOrganization, $eq: playerStatus },
      }),
    };
  }

  async searchProfiles(
    organizationId: OrganizationId,
    teamId?: TeamId,
    playerStatus?: PlayerTeamStatus,
  ): Promise<UnexpectedError | ParsingError | PlayerTeamProfile[]> {
    return this.collection
      .find(this.buildGetByOrganizationMongoQuery(organizationId, teamId, playerStatus))
      .toArray()
      .then((players) => {
        if (players) {
          const asEntity = PlayerTeamProfile.toManyEntities(players);
          if (isError(asEntity)) {
            // TODO: log
          }

          return asEntity;
        }
        return [];
      })
      .catch((err) => {
        // TODO: log
        return new UnexpectedError(err);
      });
  }

  async findByOrganizationAndPlayer(
    organizationId: OrganizationId,
    playerId: PlayerId,
  ): Promise<UnexpectedError | ParsingError | PlayerTeamProfile[]> {
    return this.collection
      .find({ organizationId, playerId, status: { $ne: PlayerTeamStatus.RemovedOrganization } })
      .toArray()
      .then((playerTeamProfiles) => {
        if (playerTeamProfiles) {
          const asEntity = PlayerTeamProfile.toManyEntities(playerTeamProfiles);
          if (isError(asEntity)) {
            // TODO: log
          }

          return asEntity;
        }
        return [];
      })
      .catch((err) => {
        // TODO: log
        return new UnexpectedError(err);
      });
  }

  async findByPlayerId(
    playerId: PlayerId,
  ): Promise<UnexpectedError | ParsingError | PlayerTeamProfile[]> {
    return this.collection
      .find({ playerId, status: { $ne: PlayerTeamStatus.RemovedOrganization } })
      .toArray()
      .then((playerTeamProfiles) => {
        if (playerTeamProfiles) {
          const asEntity = PlayerTeamProfile.toManyEntities(playerTeamProfiles);
          if (isError(asEntity)) {
            // TODO: log
          }

          return asEntity;
        }

        return [];
      });
  }

  async findByOrganizationAndPlayers(
    organizationId: OrganizationId,
    playerIds: PlayerId[],
  ): Promise<UnexpectedError | ParsingError | PlayerTeamProfile[]> {
    return this.collection
      .find({
        organizationId,
        playerId: { $in: playerIds },
        status: { $ne: PlayerTeamStatus.RemovedOrganization },
      })
      .toArray()
      .then((playerTeamProfiles) => {
        if (playerTeamProfiles) {
          const asEntity = PlayerTeamProfile.toManyEntities(playerTeamProfiles);
          if (isError(asEntity)) {
            // TODO: log
          }

          return asEntity;
        }
        return [];
      })
      .catch((err) => {
        // TODO: log
        return new UnexpectedError(err);
      });
  }

  async findByPlayerIds(
    playerIds: PlayerId[],
    organizationId: OrganizationId,
  ): Promise<UnexpectedError | ParsingError | PlayerTeamProfile[]> {
    return this.collection
      .find({
        organizationId,
        playerId: { $in: playerIds },
        status: { $ne: PlayerTeamStatus.RemovedOrganization },
      })
      .toArray()
      .then((playerTeamProfile) => {
        if (playerTeamProfile) {
          const asEntity = PlayerTeamProfile.toManyEntities(playerTeamProfile);

          if (isError(asEntity)) {
            // TODO: log
          }

          return asEntity;
        }

        return [];
      })
      .catch((err) => {
        // TODO: log
        return new UnexpectedError(err);
      });
  }

  async findAssignedByPlayerIds(
    playerIds: PlayerId[],
    organizationId: OrganizationId,
  ): Promise<UnexpectedError | ParsingError | AssignedPlayerTeamProfile[]> {
    return this.collection
      .find({
        organizationId,
        playerId: { $in: playerIds },
        status: { $ne: PlayerTeamStatus.RemovedOrganization },
      })
      .toArray()
      .then((playerTeamProfile) => {
        if (playerTeamProfile) {
          const asEntity = PlayerTeamProfile.toManyAssignedPlayerEntities(playerTeamProfile);

          if (isError(asEntity)) {
            // TODO: log
          }

          return asEntity;
        }

        return [];
      })
      .catch((err) => {
        // TODO: log
        return new UnexpectedError(err);
      });
  }

  async findAssignedByPlayerTeamProfileIds(
    playerTeamProfileIds: PlayerTeamProfileId[],
  ): Promise<UnexpectedError | ParsingError | AssignedPlayerTeamProfile[]> {
    return this.collection
      .find({
        id: { $in: playerTeamProfileIds },
        status: { $ne: PlayerTeamStatus.RemovedOrganization },
      })
      .toArray()
      .then((playerTeamProfile) => {
        if (playerTeamProfile) {
          const asEntity = PlayerTeamProfile.toManyAssignedPlayerEntities(playerTeamProfile);

          if (isError(asEntity)) {
            // TODO: log
          }

          return asEntity;
        }

        return [];
      })
      .catch((err) => {
        // TODO: log
        return new UnexpectedError(err);
      });
  }

  getPlayersStatsByCategory(
    teamId: TeamId,
    foreignField: StatIndividualRole,
  ): Promise<UnexpectedError | ParsingError | FootballMatchStats.TeamPlayersStats[]> {
    return this.collection
      .aggregate([
        {
          $match: {
            teamId,
          },
        },
        {
          $lookup: {
            from: "football-match-goals-scored",
            localField: "id",
            foreignField: foreignField,
            as: "playerGoals",
          },
        },
        {
          $lookup: {
            from: "football-match-players-reviews",
            localField: "id",
            foreignField: "playerTeamProfileId",
            as: "playerReviews",
          },
        },
        // Step 4: Group reviews by match to get unique appearances
        {
          $addFields: {
            uniqueMatches: {
              $size: {
                $setUnion: "$playerReviews.footballMatchPlanId",
              },
            },
          },
        },
        {
          $addFields: {
            stat: { $size: "$playerGoals" },
            appearances: "$uniqueMatches",
            statPerMatch: {
              $cond: {
                if: { $eq: ["$uniqueMatches", 0] },
                then: 0,
                else: {
                  $round: [
                    {
                      $divide: [{ $size: "$playerGoals" }, { $max: ["$uniqueMatches", 1] }],
                    },
                    2,
                  ],
                },
              },
            },
          },
        },
        {
          $project: {
            playerTeamProfileId: "$id",
            playerId: "$playerId",
            firstName: 1,
            lastName: 1,
            stat: 1,
            appearances: 1,
            statPerMatch: 1,
          },
        },
      ])
      .toArray()
      .then((plans) => {
        if (plans) {
          const parsed = FootballMatchStats.Entity.toPlayersGoalsStatsEntities(plans);

          if (isError(parsed)) {
            parsed.addContext({
              service: PlayerTeamProfileRepository.name,
              method: this.getPlayersStatsByCategory.name,
              operation: FootballMatchStats.Entity.toPlayersGoalsStatsEntities.name,
              teamId,
            });

            return parsed;
          }

          return parsed;
        }

        return [];
      })
      .catch((err) => {
        console.error(err);
        return new UnexpectedError(err, {
          service: PlayerTeamProfileRepository.name,
          method: this.getPlayersStatsByCategory.name,
          operation: this.collection.find.name,
        });
      });
  }

  //#endregion
}
