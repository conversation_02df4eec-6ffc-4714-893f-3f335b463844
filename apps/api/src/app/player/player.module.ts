import { forwardRef, Module } from "@nestjs/common";

import { DatabaseModule } from "../database";
import { PermissionsModule } from "../permissions";
import { PlayerTeamProfileModule } from "../player-team-profile";
import { PlayerUsersModule } from "../player-users/player-users.module";
import { ProfileModule } from "../profile/profile.module";
import { PlayerController } from "./player.controller";
import { PlayerRepository } from "./player.repository";
import { PlayerService } from "./player.service";
import { AppConfigModule } from "../config";
import { AssetsSharedModule } from "../assets/assets.shared.module";
import { TeamModule } from "../team/team.module";
import { PaymentRequestsModule } from "../payments/payment-requests.module";
import { GuardianRepository } from "./guardian.repository";

@Module({
  imports: [
    DatabaseModule,
    PlayerTeamProfileModule,
    ProfileModule,
    PermissionsModule,
    forwardRef(() => PlayerUsersModule),
    AppConfigModule,
    AssetsSharedModule,
    TeamModule,
    PaymentRequestsModule,
  ],
  providers: [PlayerRepository, PlayerService, GuardianRepository],
  controllers: [PlayerController],
  exports: [PlayerService, PlayerRepository],
})
export class PlayerModule {}
