import { Injectable, Inject, Logger } from "@nestjs/common";
import { ClientSession, MongoClient } from "mongodb";

import {
  Primitive,
  UnexpectedError,
  Player,
  isError,
  PlayerId,
  ParsingError,
  GetPlayerByGuardianDto,
  PlayersSearchParams,
  PopulatedPlayer,
  Pagination,
  TeamId,
  PlayerTeamProfile,
  DomainError,
  ErrorMessages,
  CustomError,
  PlayerSorting,
  Email,
  PlayerTeamStatus,
} from "@mio/helpers";

import {
  BaseRepository,
  DATABASE_CLIENT,
  DATABASE_CONNECTION,
  DBClient,
  DBCollection,
} from "../database";
import { playerTeamProfileCollection } from "../player-team-profile";
import { IMAGE_ASSETS_COLLECTION } from "../assets/image.repository";

export const playersCollection = "players";

type PlayerDocument = Primitive<Player>;
type PlayerTeamProfileDocument = Primitive<PlayerTeamProfile>;

@Injectable()
export class PlayerRepository extends BaseRepository {
  private collection: DBCollection<PlayerDocument>;
  private profilesCollection: DBCollection<PlayerTeamProfileDocument>;
  protected readonly logger = new Logger(PlayerRepository.name);

  constructor(
    @Inject(DATABASE_CONNECTION) db: DBClient,
    @Inject(DATABASE_CLIENT) mongoClient: MongoClient,
  ) {
    super(db, mongoClient);

    this.collection = this.db.collection(playersCollection);
    this.profilesCollection = this.db.collection(playerTeamProfileCollection);

    this.createPlayerIndexes();
  }

  async createPlayer(player: Player, session?: ClientSession): Promise<UnexpectedError | void> {
    return this.collection
      .insertOne({ ...player }, { session })
      .then(() => undefined)
      .catch((err) => {
        return new UnexpectedError(err, {
          service: PlayerRepository.name,
          method: this.createPlayer.name,
          operation: this.collection.insertOne.name,
          player,
        });
      });
  }

  async getByIds(ids: PlayerId[]): Promise<UnexpectedError | ParsingError | Player[]> {
    return this.collection
      .find({ id: { $in: ids } })
      .toArray()
      .then((players) => {
        if (players) {
          const entities = Player.toManyEntities(players);
          if (isError(entities)) {
            entities.addContext({
              service: PlayerRepository.name,
              method: this.getByIds.name,
              operation: Player.toManyEntities.name,
              ids,
            });
          }
          return entities;
        }
        return [];
      })
      .catch((err) => {
        return new UnexpectedError(err, {
          service: PlayerRepository.name,
          method: this.getByIds.name,
          operation: this.collection.find.name,
          ids,
        });
      });
  }

  async updatePlayer(
    player: Player,
    session?: ClientSession,
  ): Promise<undefined | DomainError | UnexpectedError> {
    const result = await this.collection
      .updateOne({ id: player.id }, { $set: player }, { session })
      .catch((err) => {
        this.logger.error("Error updating player:", err);
        return new UnexpectedError(err, {
          service: PlayerRepository.name,
          method: this.updatePlayer.name,
          operation: this.collection.updateOne.name,
          player,
        });
      });

    if (isError(result)) {
      return result;
    }

    if (result.matchedCount < 1) {
      return new DomainError(ErrorMessages.EntityNotFound, {
        service: PlayerRepository.name,
        method: this.updatePlayer.name,
        operation: this.collection.updateOne.name,
        player,
      });
    }

    if (result.matchedCount > 1) {
      return new DomainError(ErrorMessages.MultipleEntitiesFound, {
        service: PlayerRepository.name,
        method: this.updatePlayer.name,
        operation: this.collection.updateOne.name,
        player,
      });
    }

    return undefined;
  }

  async getPlayersByTeamAndIds(
    teamId: TeamId,
    ids: PlayerId[],
  ): Promise<UnexpectedError | ParsingError | Player[]> {
    /*
    Querying players by only teamId is not enough, because some players might
    have a historic review but not be part of the team anymore. Therefore we
    also query by existingReview.playerId for each existing review.
    */

    /* 
     1. Fetch all PlaterTeamProfiles
     2. Combine each with its corresponding Player document
     3. Trick: Make each Player the root document ($replaceRoot), because after all we want to return Players[].
     4. Combine each player with its related PlayerTeamProfiles[]
    */

    const cursor = this.profilesCollection.aggregate([
      {
        $match: { $or: [{ teamId }, { playerId: { $in: ids } }] },
      },
      {
        $sort: { firstName: 1, lastName: 1 },
      },
    ]);

    return cursor
      .toArray()
      .then((results) => {
        const parsed = Player.toManyEntities(results);

        if (isError(parsed)) {
          return parsed.addContext({
            service: PlayerRepository.name,
            method: this.getPlayersByTeamAndIds.name,
            operation: Player.toManyEntities.name,
            data: results,
            params: { teamId, ids },
          });
        }

        return parsed;
      })
      .catch((err) => {
        return new UnexpectedError(err, {
          service: PlayerRepository.name,
          method: this.getPlayersByTeamAndIds.name,
          operation: this.collection.find.name,
          params: { teamId, ids },
        });
      });
  }

  async searchPlayers(
    params: PlayersSearchParams,
  ): Promise<UnexpectedError | ParsingError | Pagination.PaginatedData<PopulatedPlayer>> {
    const {
      query: {
        dobBefore,
        dobAfter,
        searchName,
        teamId,
        playerStatus,
        playerStatusNot,
        pagination,
        sortBy,
      },
    } = params;

    const createSortingOption = (type: PlayerSorting) => {
      switch (type) {
        case PlayerSorting.Names:
          return { firstName: 1, lastName: 1 };
        case PlayerSorting.Age:
          return { dob: 1 };
        default:
          return { _id: -1 };
      }
    };

    /*
    1. Match documents based on filters: organizationId, status, and teamId.
    2. Lookup playersCollection and combine each document with its corresponding player.
    3. Replace the root document with the player field to prepare for returning populated players. Deduplicate.
    4. Lookup playerTeamProfileCollection and combine each player with their related PlayerTeamProfiles within the given organization.
    5. Sort and paginate the results.
    6. Return the final result with total count and paginated player documents.
    */

    const stages = [
      {
        $match: {
          organizationId: params.orgId,
          teamId: teamId,
          status: {
            $ne: PlayerTeamStatus.RemovedOrganization, // Always exclude removed-organization
            ...(playerStatus && { $eq: playerStatus }),
            ...(playerStatusNot && { $nin: playerStatusNot }),
          },
        },
      },
      {
        $lookup: {
          from: playersCollection,
          localField: "playerId",
          foreignField: "id",
          as: "player",
        },
      },
      {
        $unwind: {
          path: "$player",
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $addFields: {
          // Only include guardian if it exists and is not null
          "player.guardian": {
            $cond: {
              if: { $ne: ["$player.guardian", null] },
              then: "$player.guardian",
              else: "$$REMOVE",
            },
          },
          // TODO: double check whether we have to do this in the db layer?
          // Ensure status is a valid enum value
          status: {
            $cond: {
              if: { $eq: ["$status", "applicant-organization"] },
              then: "$status",
              else: "applicant-organization", // default value
            },
          },
          // TODO: double check whether we have to do this in the db layer?
          // Ensure updatedAt is a valid date
          updatedAt: {
            $cond: {
              if: { $type: "$updatedAt" },
              then: "$updatedAt",
              else: new Date(),
            },
          },
        },
      },
      { $replaceRoot: { newRoot: "$player" } },
      { $group: { _id: "$id", player: { $first: "$$ROOT" } } },
      { $replaceRoot: { newRoot: "$player" } },
      {
        $match: {
          ...(dobBefore && dobAfter && { dob: { $lte: dobBefore, $gte: dobAfter } }),
          ...(!dobAfter && dobBefore && { dob: { $lte: dobBefore } }),
          ...(!dobBefore && dobAfter && { dob: { $gte: dobAfter } }),
          ...(searchName && {
            $or: [
              {
                firstName: { $regex: new RegExp(searchName, "i") },
              },
              {
                lastName: { $regex: new RegExp(searchName, "i") },
              },
            ],
          }),
        },
      },
      {
        $lookup: {
          from: playerTeamProfileCollection,
          let: {
            playerId: "$id",
          },
          pipeline: [
            {
              $match: {
                organizationId: params.orgId,
                $expr: {
                  $eq: ["$$playerId", "$playerId"],
                },
              },
            },
          ],
          as: "profiles",
        },
      },
      {
        $lookup: {
          from: IMAGE_ASSETS_COLLECTION,
          localField: "documents.photo_id",
          foreignField: "id",
          as: "photo",
        },
      },
      {
        $unwind: {
          path: "$photo",
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $lookup: {
          from: IMAGE_ASSETS_COLLECTION,
          localField: "documents.image_document_ids",
          foreignField: "id",
          as: "document_images",
        },
      },
      { $sort: createSortingOption(sortBy) },
      {
        $facet: {
          total: [{ $count: "count" }],
          results: [{ $skip: pagination.skip }, { $limit: pagination.limit }],
        },
      },
    ];

    const cursor = this.profilesCollection.aggregate(stages);

    return cursor
      .toArray()
      .then((results) => {
        const parsed = Player.toManyPopulatedEntities(results[0].results);

        if (isError(parsed)) {
          parsed.addContext({
            service: PlayerRepository.name,
            method: this.searchPlayers.name,
            operation: Player.toManyPopulatedEntities.name,
            data: results,
            params,
          });

          return parsed;
        }

        return {
          total: results[0].total[0]?.count ?? 0,
          data: parsed,
          skip: params.query.pagination.skip,
          limit: params.query.pagination.limit,
        };
      })
      .catch((err) => {
        return new UnexpectedError(err);
      });
  }

  async findRelatedPlayers(email: Email) {
    return this.collection
      .find({ $or: [{ email }, { "guardian.email": email }] })
      .toArray()
      .then((result) => {
        if (result) {
          const parsed = Player.toManyEntities(result);

          if (isError(parsed)) {
            parsed.addContext({
              service: PlayerRepository.name,
              method: this.findRelatedPlayers.name,
              operation: Player.toManyEntities.name,
              data: result,
              email,
            });
          }

          return parsed;
        }

        return [];
      })
      .catch((err) => {
        return new UnexpectedError(err, {
          service: PlayerRepository.name,
          method: this.findRelatedPlayers.name,
          operation: this.collection.find.name,
          email,
        });
      });
  }

  async findByEmail(email: Email): Promise<Player | null | UnexpectedError | ParsingError> {
    return this.collection
      .findOne({ email })
      .then((player) => {
        if (player) {
          const asEntity = Player.toEntity(player);
          if (isError(asEntity)) {
            return asEntity.addContext({
              service: PlayerRepository.name,
              method: this.findByEmail.name,
              operation: Player.toEntity.name,
              data: player,
              email,
            });
          }
        }
        return null;
      })
      .catch((err) => {
        return new UnexpectedError(err, {
          service: PlayerRepository.name,
          method: this.findByEmail.name,
          operation: this.collection.findOne.name,
          email,
        });
      });
  }

  async findByGuardian(
    dto: GetPlayerByGuardianDto,
  ): Promise<Player | null | UnexpectedError | ParsingError> {
    return this.collection
      .findOne({
        "guardian.email": dto.guardianEmail,
        firstName: dto.firstName,
        lastName: dto.lastName,
      })
      .then((player) => {
        if (player) {
          const asEntity = Player.toEntity(player);
          if (isError(asEntity)) {
            return asEntity.addContext({
              service: PlayerRepository.name,
              method: this.findByGuardian.name,
              operation: Player.toEntity.name,
              data: player,
              dto,
            });
          }

          return asEntity;
        }
        return null;
      })
      .catch((err) => {
        return new UnexpectedError(err, {
          service: PlayerRepository.name,
          method: this.findByGuardian.name,
          operation: this.collection.findOne.name,
          dto,
        });
      });
  }

  async getPopulatedPlayer(id: PlayerId): Promise<CustomError | PopulatedPlayer> {
    return this.collection
      .aggregate([
        {
          $match: { id },
        },
        {
          $lookup: {
            from: playerTeamProfileCollection,
            localField: "id",
            foreignField: "playerId",
            as: "profiles",
          },
        },
        {
          $lookup: {
            from: IMAGE_ASSETS_COLLECTION,
            localField: "documents.photo_id",
            foreignField: "id",
            as: "photo",
          },
        },
        {
          $unwind: {
            path: "$photo",
            preserveNullAndEmptyArrays: true,
          },
        },
        {
          $lookup: {
            from: IMAGE_ASSETS_COLLECTION,
            localField: "documents.image_document_ids",
            foreignField: "id",
            as: "document_images",
          },
        },
      ])
      .toArray()
      .then((results) => {
        const parsed = Player.toManyPopulatedEntities(results);

        if (isError(parsed)) {
          return parsed.addContext({
            service: PlayerRepository.name,
            method: this.getPopulatedPlayer.name,
            operation: Player.toManyPopulatedEntities.name,
            data: results,
            id,
          });
        }

        if (parsed.length === 0) {
          return new DomainError(ErrorMessages.EntityNotFound, {
            service: PlayerRepository.name,
            method: this.getPopulatedPlayer.name,
            operation: Player.toManyPopulatedEntities.name,
            data: results,
            id,
          });
        }

        if (parsed.length > 1) {
          return new DomainError(ErrorMessages.MultipleEntitiesFound, {
            service: PlayerRepository.name,
            method: this.getPopulatedPlayer.name,
            operation: Player.toManyPopulatedEntities.name,
            data: results,
            id,
          });
        }

        return parsed[0];
      })
      .catch((err) => {
        return new UnexpectedError(err, {
          service: PlayerRepository.name,
          method: this.getPopulatedPlayer.name,
          operation: this.collection.aggregate.name,
          id,
        });
      });
  }

  async getById(id: PlayerId): Promise<UnexpectedError | ParsingError | Player | null> {
    return this.collection
      .findOne({ id })
      .then((player) => {
        if (player) {
          const entity = Player.toEntity(player);

          if (isError(entity)) {
            entity.addContext({
              service: PlayerRepository.name,
              method: this.getById.name,
              operation: Player.toEntity.name,
              id,
            });
          }

          return entity;
        }
        return null;
      })
      .catch((err) => {
        return new UnexpectedError(err, {
          service: PlayerRepository.name,
          method: this.getById.name,
          operation: this.collection.findOne.name,
          id,
        });
      });
  }

  private async createPlayerIndexes(): Promise<void> {
    try {
      // Primary indexes
      await this.collection.createIndex({ id: 1 }, { unique: true });
      await this.collection.createIndex({ email: 1 });
      await this.collection.createIndex({ "guardian.email": 1 });
      await this.collection.createIndex({ firstName: 1, lastName: 1 });

      // Document/Image indexes
      await this.collection.createIndex({ "documents.photo_id": 1 });
      await this.collection.createIndex({ "documents.image_document_ids": 1 });
    } catch (err) {
      this.logger.error("Error creating player indexes:", err);
    }
  }
}
