import { Inject, Injectable, Logger } from "@nestjs/common";
import { ClientSession } from "mongodb";

import { Guardian, toEntityOrThrow } from "@mio/helpers/lib/types/player/Guardian";
import { UnexpectedError, type Primitive } from "@mio/helpers/lib/types/shared";
import { DATABASE_CONNECTION, DBClient, DBCollection } from "../database";

export const guardiansCollection = "guardians";

type GuardianDocument = Primitive<Guardian>;

@Injectable()
export class GuardianRepository {
  private collection: DBCollection<GuardianDocument>;

  private logger: Logger;

  constructor(@Inject(DATABASE_CONNECTION) private db: DBClient) {
    this.collection = db.collection(guardiansCollection);

    this.logger = new Logger(this.constructor.name);

    this.collection.createIndex({ id: 1 }, { unique: true }).catch((err) => {
      this.logger.error("Failed to create id index for guardians collection", err);
    });

    this.collection.createIndex({ email: 1 }, { unique: true }).catch((err) => {
      this.logger.error("Failed to create email index for guardians collection", err);
    });
  }

  async createGuardian(guardian: Guardian, session?: ClientSession): Promise<void> {
    return this.collection
      .insertOne({ ...guardian }, { session })
      .then(() => undefined)
      .catch((err) => {
        throw new UnexpectedError(err, {
          service: GuardianRepository.name,
          method: this.createGuardian.name,
          operation: this.collection.insertOne.name,
          params: { guardian },
        });
      });
  }

  async updateGuardian(guardian: Guardian, session?: ClientSession): Promise<void> {
    return this.collection
      .replaceOne({ id: guardian.id }, guardian, { session })
      .then(() => undefined)
      .catch((err: unknown) => {
        throw new UnexpectedError(err, {
          service: GuardianRepository.name,
          method: this.updateGuardian.name,
          operation: "updateOne",
          params: { guardian },
        });
      });
  }

  async getOneByEmail(email: Guardian["email"]): Promise<Guardian | null> {
    const result = await this.collection.findOne({ email });

    if (!result) {
      return null;
    }

    return toEntityOrThrow(result);
  }

  async getOneById(id: Guardian["id"]): Promise<Guardian | null> {
    const result = await this.collection.findOne({ id });

    if (!result) {
      return null;
    }

    return toEntityOrThrow(result);
  }
}
