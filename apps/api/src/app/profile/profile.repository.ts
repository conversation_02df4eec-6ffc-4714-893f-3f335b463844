import { Inject, Injectable, Logger } from "@nestjs/common";

import {
  isError,
  ParsingError,
  Primitive,
  Profile,
  ProfileId,
  UnexpectedError,
  CoachUserId,
  PublicProfile,
  DomainError,
  ErrorMessages,
  TeamId,
  OrganizationId,
  PermissionsModule,
} from "@mio/helpers";
import { DATABASE_CONNECTION, DBClient, DBCollection } from "../database";
import { organizationsCollection } from "../organization/organization.repository";
import { PermissionDocument, permissionsCollection } from "../permissions/permissions.repository";
import { permissionRolesCollection } from "../permissions/roles.repository";

type ProfileDocument = Primitive<Profile>;

export const profilesCollection = "profiles";

@Injectable()
export class ProfileRepository {
  private collection: DBCollection<ProfileDocument>;
  private permissionsCollection: DBCollection<PermissionDocument>;

  private logger: Logger;

  constructor(@Inject(DATABASE_CONNECTION) private db: DBClient) {
    this.collection = db.collection(profilesCollection);
    this.permissionsCollection = db.collection(permissionsCollection);

    this.logger = new Logger(this.constructor.name);

    this.createProfileIndexes();
  }

  async create(profile: Profile): Promise<UnexpectedError | void> {
    return this.collection
      .insertOne({ ...profile })
      .then(() => undefined)
      .catch((err) => {
        // TODO: log
        return new UnexpectedError(err);
      });
  }

  async getManyByIds(ids: ProfileId[]): Promise<Profile[] | ParsingError | UnexpectedError> {
    return this.collection
      .find({ id: { $in: ids } })
      .toArray()
      .then((profiles) => {
        const entities = Profile.toEntities(profiles);

        if (isError(entities)) {
          // TODO: log
        }

        return entities;
      })
      .catch((err) => {
        // TODO: log
        return new UnexpectedError(err);
      });
  }

  async getByUserId(user: CoachUserId): Promise<ParsingError | UnexpectedError | Profile | null> {
    return this.collection
      .findOne({ user })
      .then((mbProfile) => {
        if (mbProfile) {
          const asEntity = Profile.toEntity(mbProfile);
          if (isError(asEntity)) {
            // TODO: log
          }

          return asEntity;
        }
        return null;
      })
      .catch((err) => {
        // TODO: log
        return new UnexpectedError(err);
      });
  }

  async getPublicProfileByUserId(
    user: CoachUserId,
  ): Promise<ParsingError | UnexpectedError | DomainError | PublicProfile | null> {
    const stages = [
      {
        $match: { user },
      },
      {
        $lookup: {
          from: organizationsCollection,
          localField: "id",
          foreignField: "members",
          as: "organizations",
        },
      },
    ];

    return this.collection
      .aggregate(stages)
      .toArray()
      .then((result) => {
        if (result.length > 1) {
          return new DomainError(ErrorMessages.MultipleEntitiesFound);
        }

        if (!result[0]) {
          return null;
        }

        const parsed = Profile.toPublicEntity(result[0]);

        return parsed;
      })
      .catch((err) => {
        return new UnexpectedError(err);
      });
  }
  private async createProfileIndexes(): Promise<void> {
    try {
      // Primary indexes
      await this.collection.createIndex({ id: 1 });
      await this.collection.createIndex({ user: 1 });
      await this.collection.createIndex({ playerId: 1 });
      await this.collection.createIndex({ teamId: 1 });

      // Compound indexes for complex queries
      await this.collection.createIndex({ organizationId: 1, playerId: 1 });
      await this.collection.createIndex({ organizationId: 1, teamId: 1, status: 1 });
    } catch (err) {
      this.logger.error("Error creating profile indexes:", err);
    }
  }

  async getTeamCoaches(
    teamId: TeamId,
    organizationId: OrganizationId,
  ): Promise<ParsingError | UnexpectedError | Profile[]> {
    /*

    * Finding all coaches for a team: *

    1. Being a coach to a team is determined by the presence of certain Permissions - owner or custom ones
    2. That's why we first start by finding all permissions with certain teamId / organizationId criteria
    3. Then we match each permission to the Profile it belongs to
    4. Since we want to return a Profile as the main object - we use the $replaceRoot command. We also attach Profile.user to help the UI needs
    5. Then we eliminate any duplications with the $group command ( same person might have multiple permissions for the same team for some reason 
      - it's not a problem for us but we don't need to know this twice )

    */

    return this.permissionsCollection
      .aggregate([
        {
          $match: {
            organizationId,
            $or: [{ teamIds: teamId }, { type: PermissionsModule.PermissionEntity.Type.Owner }],
          },
        },
        {
          $lookup: {
            from: permissionRolesCollection,
            localField: "roleId",
            foreignField: "id",
            as: "roles",
          },
        },
        {
          $match: {
            $or: [
              { roles: { $elemMatch: { actions: PermissionsModule.Action.Actions.ManageEvents } } },
              { type: PermissionsModule.PermissionEntity.Type.Owner },
            ],
          },
        },
        {
          $lookup: {
            from: profilesCollection,
            localField: "profileId",
            foreignField: "id",
            as: "profile",
          },
        },
        { $unwind: "$profile" },
        { $replaceRoot: { newRoot: "$profile" } },
        {
          $group: {
            _id: "$id",
            id: { $first: "$id" },
            firstName: { $first: "$firstName" },
            lastName: { $first: "$lastName" },
            user: { $first: "$user" },
          },
        },
        {
          $sort: { firstName: 1 },
        },
      ])
      .toArray()
      .then((results) => {
        return Profile.toEntities(results);
      })
      .catch((err) => {
        // TODO: log
        return new UnexpectedError(err);
      });
  }
}
