import { Injectable } from "@nestjs/common";
import { isNil } from "lodash/fp";

import {
  CreateFinancialIntegrationDto,
  DomainError,
  ErrorMessages,
  FinancialIntegration,
  FinancialIntegrationId,
  FinancialIntegrationType,
  isError,
  OrganizationId,
  UpdateFinancialIntegrationDto,
} from "@mio/helpers";

import { FinancialIntegrationRepository } from "./financial-integration.repository";
import { EncryptionService } from "../encryption/encryption.service";

@Injectable()
export class FinancialIntegrationService {
  constructor(
    private repo: FinancialIntegrationRepository,
    private encryptionService: EncryptionService,
  ) {}

  async create(dto: CreateFinancialIntegrationDto) {
    const encryptedValues = this.encryptionService.encrypt(dto.key);

    if (isError(encryptedValues)) {
      encryptedValues.addContext({
        service: FinancialIntegrationService.name,
        method: this.create.name,
        operation: this.encryptionService.encrypt.name,
        dto,
      });

      return encryptedValues;
    }

    const integration = FinancialIntegration.create(
      dto,
      encryptedValues.encryptedKey,
      encryptedValues.iv,
    );

    const result = await this.repo.create(integration);

    if (isError(result)) {
      result.addContext({
        service: FinancialIntegrationService.name,
        method: this.create.name,
        operation: this.repo.create.name,
        dto,
      });

      return result;
    }

    return integration;
  }

  async update(dto: UpdateFinancialIntegrationDto) {
    const financialIntegration = await this.repo.getById(dto.id);

    if (isError(financialIntegration)) {
      return financialIntegration;
    }

    if (isNil(financialIntegration)) {
      return new DomainError(ErrorMessages.EntityNotFound, {
        service: FinancialIntegrationService.name,
        method: this.update.name,
        operation: this.repo.getById.name,
        dto,
      });
    }

    const updatedIntegration = FinancialIntegration.update(dto, financialIntegration);

    const result = await this.repo.update(updatedIntegration);

    if (isError(result)) {
      result.addContext({
        service: FinancialIntegrationService.name,
        method: this.update.name,
        operation: this.repo.update.name,
        dto,
      });

      return result;
    }

    return updatedIntegration;
  }

  async delete(id: FinancialIntegrationId) {
    const integration = await this.repo.getById(id);

    if (isError(integration)) {
      integration.addContext({
        service: FinancialIntegrationService.name,
        method: this.delete.name,
        operation: this.repo.getById.name,
        id,
      });

      return integration;
    }

    if (isNil(integration)) {
      return new DomainError(ErrorMessages.EntityNotFound, {
        service: FinancialIntegrationService.name,
        method: this.delete.name,
        operation: this.repo.getById.name,
        id,
      });
    }

    const result = await this.repo.delete(id);

    if (isError(result)) {
      result.addContext({
        service: FinancialIntegrationService.name,
        method: this.delete.name,
        operation: this.repo.delete.name,
        id,
      });

      return result;
    }

    return undefined;
  }

  async findByOrganizationId(organizationId: OrganizationId) {
    const result = await this.repo.findByOrganizationId(organizationId);

    if (isError(result)) {
      result.addContext({
        service: FinancialIntegrationService.name,
        method: this.findByOrganizationId.name,
        operation: this.repo.findByOrganizationId.name,
        organizationId,
      });
    }

    return result;
  }

  async isStripe(organizationId: OrganizationId) {
    const result = await this.repo.getByOrganizationIdAndType(
      organizationId,
      FinancialIntegrationType.Stripe,
    );

    if (isError(result)) {
      result.addContext({
        service: FinancialIntegrationService.name,
        method: this.isStripe.name,
        operation: this.repo.getByOrganizationIdAndType.name,
        organizationId,
      });
    }

    return result?.type === FinancialIntegrationType.Stripe;
  }

  async getByOrganizationIdAndType(organizationId: OrganizationId, type: FinancialIntegrationType) {
    const result = await this.repo.getByOrganizationIdAndType(organizationId, type);

    if (isError(result)) {
      result.addContext({
        service: FinancialIntegrationService.name,
        method: this.getByOrganizationIdAndType.name,
        operation: this.repo.getByOrganizationIdAndType.name,
        type,
        organizationId,
      });
    }

    return result;
  }

  async getById(id: FinancialIntegrationId) {
    const result = await this.repo.getById(id);

    if (isError(result)) {
      result.addContext({
        service: FinancialIntegrationService.name,
        method: this.getById.name,
        operation: this.repo.getById.name,
        id,
      });
    }

    return result;
  }
}
