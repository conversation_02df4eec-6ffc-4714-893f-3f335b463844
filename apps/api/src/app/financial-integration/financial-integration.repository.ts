import { Inject, Injectable, Logger } from "@nestjs/common";

import {
  Primitive,
  UnexpectedError,
  isError,
  OrganizationId,
  DomainError,
  ErrorMessages,
  FinancialIntegration,
  FinancialIntegrationId,
  FinancialIntegrationType,
} from "@mio/helpers";

import { DATABASE_CONNECTION, DBClient, DBCollection } from "../database";

type FinancialIntegrationDocument = Primitive<FinancialIntegration>;

@Injectable()
export class FinancialIntegrationRepository {
  private collection: DBCollection<FinancialIntegrationDocument>;
  private readonly logger = new Logger(FinancialIntegrationRepository.name);

  constructor(@Inject(DATABASE_CONNECTION) private db: DBClient) {
    this.collection = db.collection("financial-integrations");
    this.createIndexes();
  }

  private async createIndexes() {
    try {
      await this.collection.createIndex({ id: 1 }, { unique: true });
      await this.collection.createIndex({ organizationId: 1 });
      await this.collection.createIndex(
        { organizationId: 1, type: 1 },
        { unique: true, name: "financial-integration-org-type-unique" }, // avoid index name duplication warnings
      );
    } catch (err) {
      this.logger.error(`Error creating indexes for ${this.constructor.name}`, err);
    }
  }

  async create(integration: FinancialIntegration): Promise<UnexpectedError | DomainError | void> {
    return this.collection
      .insertOne({ ...integration })
      .then(() => undefined)
      .catch((err) => {
        if (err.code === 11000) {
          // DuplicateKey - integration type already exists for this org
          return new DomainError(ErrorMessages.EntityAlreadyExists, {
            service: FinancialIntegrationRepository.name,
            method: this.create.name,
            integration,
          });
        }
        this.logger.error("Error creating financial integration", { err, integration });
        return new UnexpectedError(err, {
          service: FinancialIntegrationRepository.name,
          method: this.create.name,
          integration,
        });
      });
  }

  async update(integration: FinancialIntegration) {
    const result = await this.collection
      .updateOne({ id: integration.id }, { $set: integration })
      .catch((err) => {
        this.logger.error("Error updating financial integration", { err, integration });
        return new UnexpectedError(err, {
          service: FinancialIntegrationRepository.name,
          method: this.update.name,
          integration,
        });
      });

    if (isError(result)) {
      return result;
    }

    if (result.matchedCount < 1) {
      return new DomainError(ErrorMessages.EntityNotFound, {
        service: FinancialIntegrationRepository.name,
        method: this.update.name,
        integration,
      });
    }

    return undefined;
  }

  async delete(integrationId: FinancialIntegrationId) {
    return this.collection
      .deleteOne({
        id: integrationId,
      })
      .then(() => undefined)
      .catch((err) => {
        this.logger.error("Error deleting financial integration", { err, integrationId });
        return new UnexpectedError(err, {
          service: FinancialIntegrationRepository.name,
          method: this.delete.name,
          integrationId,
        });
      });
  }

  async findByOrganizationId(organizationId: OrganizationId) {
    return this.collection
      .find({ organizationId })
      .toArray()
      .then((mbFinancialIntegrations) => {
        if (mbFinancialIntegrations) {
          const asEntity = FinancialIntegration.toEntities(mbFinancialIntegrations);
          if (isError(asEntity)) {
            this.logger.error("Error parsing financial integrations", {
              error: asEntity,
              organizationId,
            });
          }

          return asEntity;
        }

        return [];
      })
      .catch((err) => {
        this.logger.error("Error finding financial integrations by organizationId", {
          err,
          organizationId,
        });
        return new UnexpectedError(err, {
          service: FinancialIntegrationRepository.name,
          method: this.findByOrganizationId.name,
          organizationId,
        });
      });
  }

  async getByOrganizationIdAndType(organizationId: OrganizationId, type: FinancialIntegrationType) {
    return this.collection
      .findOne({ organizationId, type })
      .then((mbFinancialIntegration) => {
        if (mbFinancialIntegration) {
          const asEntity = FinancialIntegration.toEntity(mbFinancialIntegration);
          if (isError(asEntity)) {
            this.logger.error("Error parsing financial integration", {
              error: asEntity,
              organizationId,
              type,
            });
          }

          return asEntity;
        }

        return null;
      })
      .catch((err) => {
        this.logger.error("Error getting financial integration by org and type", {
          err,
          organizationId,
          type,
        });
        return new UnexpectedError(err, {
          service: FinancialIntegrationRepository.name,
          method: this.getByOrganizationIdAndType.name,
          organizationId,
          type,
        });
      });
  }

  async getById(id: FinancialIntegrationId) {
    return this.collection
      .findOne({ id })
      .then((mbFinancialIntegration) => {
        if (mbFinancialIntegration) {
          const asEntity = FinancialIntegration.toEntity(mbFinancialIntegration);
          if (isError(asEntity)) {
            this.logger.error("Error parsing financial integration", { error: asEntity, id });
          }

          return asEntity;
        }

        return null;
      })
      .catch((err) => {
        this.logger.error("Error getting financial integration by id", { err, id });
        return new UnexpectedError(err, {
          service: FinancialIntegrationRepository.name,
          method: this.getById.name,
          id,
        });
      });
  }
}
