import { Inject, Injectable, Logger } from "@nestjs/common";

import {
  Primitive,
  UnexpectedError,
  FootballMatchPlayerReview,
  FootballMatchPlan,
  isError,
  ParsingError,
  ProfileId,
  TeamId,
} from "@mio/helpers";

import { DATABASE_CONNECTION, DBClient, DBCollection } from "../../database";

type FootballMatchReviewDocument = Primitive<FootballMatchPlayerReview.CompletePlayerReview>;

export const footballMatchPlayersReviewCollection = "football-match-players-reviews";

@Injectable()
export class FootballMatchPlayerReviewRepository {
  private collection: DBCollection<FootballMatchReviewDocument>;
  private readonly logger = new Logger(FootballMatchPlayerReviewRepository.name);

  constructor(@Inject(DATABASE_CONNECTION) private db: DBClient) {
    this.collection = db.collection(footballMatchPlayersReviewCollection);
    this.createIndexes();
  }

  private async createIndexes() {
    try {
      await this.collection.createIndex({
        footballMatchPlanId: 1,
        playerTeamProfileId: 1,
        coachId: 1,
      });
      await this.collection.createIndex({ footballMatchPlanId: 1, coachId: 1 });
      await this.collection.createIndex({ teamId: 1 });
    } catch (err) {
      this.logger.error(`Error creating indexes for ${this.constructor.name}`, err);
    }
  }

  //#region Commands

  async createMany(
    footballMatchReviews: FootballMatchPlayerReview.CompletePlayerReview[],
    coachId: ProfileId,
  ) {
    return this.collection
      .insertMany(footballMatchReviews.map((review) => ({ ...review, modifier: coachId })))
      .then(() => undefined)
      .catch((err) => {
        // TODO: log
        return new UnexpectedError(err, {
          service: FootballMatchPlayerReviewRepository.name,
          method: this.createMany.name,
          operation: this.collection.insertOne.name,
        });
      });
  }

  async updateMany(dtos: FootballMatchPlayerReview.UpdateDto[], coachId: ProfileId) {
    const bulkOperations = dtos.map((update) => ({
      updateOne: {
        filter: {
          footballMatchPlanId: update.footballMatchPlanId,
          playerTeamProfileId: update.playerTeamProfileId,
          coachId: coachId,
        },
        update: {
          $set: {
            overallRating: update.overallRating,
            overallHighlights: update.overallHighlights,
            overallMinutesPlayed: update.overallMinutesPlayed,
            overallPosition: update.overallPosition,
            attendance: update.attendance,
            modifier: coachId,
          },
        },
      },
    }));

    return this.collection
      .bulkWrite(bulkOperations)
      .then(() => undefined)
      .catch((err) => {
        return new UnexpectedError(err, {
          service: FootballMatchPlayerReviewRepository.name,
          method: this.updateMany.name,
          operation: this.collection.bulkWrite.name,
        });
      });
  }

  //#endregion

  //#region Queries

  async getByCoachId(
    footballMatchPlanId: FootballMatchPlan.Id,
    coachId: ProfileId,
  ): Promise<UnexpectedError | ParsingError | FootballMatchPlayerReview.CompletePlayerReview[]> {
    return this.collection
      .find({ footballMatchPlanId, coachId })
      .toArray()
      .then((reviews) => {
        if (reviews) {
          const parsed = FootballMatchPlayerReview.Entity.toEntities(reviews);
          if (isError(parsed)) {
            parsed.addContext({
              service: FootballMatchPlayerReviewRepository.name,
              method: this.getByCoachId.name,
              operation: FootballMatchPlayerReview.Entity.toEntities.name,
              footballMatchPlanId,
            });

            return parsed;
          }

          return parsed;
        }

        return [];
      })
      .catch((err) => {
        // TODO: log
        return new UnexpectedError(err, {
          service: FootballMatchPlayerReviewRepository.name,
          method: this.getByCoachId.name,
          operation: this.collection.find.name,
        });
      });
  }

  async getAveragePlayersRatingByTeamId(teamId: TeamId) {
    const cursor = this.collection.aggregate([
      {
        $match: {
          teamId,
        },
      },
      // First group by player and match to get unique matches
      {
        $group: {
          _id: {
            playerTeamProfileId: "$playerTeamProfileId",
            footballMatchPlanId: "$footballMatchPlanId",
          },
          // Take the average rating for this match (in case multiple coaches rated)
          matchRating: { $avg: "$overallRating" },
        },
      },
      // Then group by player to get their overall stats
      {
        $group: {
          _id: "$_id.playerTeamProfileId",
          averageOverallRating: { $avg: "$matchRating" },
          appearances: { $sum: 1 }, // Now this counts unique matches
        },
      },
      {
        $lookup: {
          from: "player-team-profiles",
          localField: "_id",
          foreignField: "id",
          as: "profileInfo",
        },
      },
      {
        $unwind: "$profileInfo",
      },
      {
        $project: {
          _id: 0,
          playerTeamProfileId: "$_id",
          averageOverallRating: {
            $round: ["$averageOverallRating", 1],
          },
          appearances: 1,
          teamId: "$profileInfo.teamId",
          firstName: "$profileInfo.firstName",
          lastName: "$profileInfo.lastName",
        },
      },
    ]);

    return cursor
      .toArray()
      .then((reviews) => {
        if (reviews) {
          const parsed = FootballMatchPlayerReview.Entity.toAverageRatingEntities(reviews);

          if (isError(parsed)) {
            parsed.addContext({
              service: FootballMatchPlayerReviewRepository.name,
              method: this.getAveragePlayersRatingByTeamId.name,
              operation: FootballMatchPlayerReview.Entity.toEntities.name,
              teamId,
            });

            return parsed;
          }

          return parsed;
        }

        return [];
      })
      .catch((err) => {
        // TODO: log
        return new UnexpectedError(err, {
          service: FootballMatchPlayerReviewRepository.name,
          method: this.getAveragePlayersRatingByTeamId.name,
          operation: this.collection.find.name,
        });
      });
  }

  //#endregion
}
