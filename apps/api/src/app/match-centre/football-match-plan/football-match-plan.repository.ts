import { Inject, Injectable, Logger } from "@nestjs/common";

import {
  Primitive,
  UnexpectedError,
  TeamId,
  ParsingError,
  isError,
  DomainError,
  ErrorMessages,
  FootballMatchPlan,
  OrganizationId,
} from "@mio/helpers";

import { DATABASE_CONNECTION, DBClient, DBCollection } from "../../database";

type FootballMatchPlanDocument = Primitive<FootballMatchPlan.CompletePlan>;

export const footballMatchPlanCollection = "football-match-plans";

@Injectable()
export class FootballMatchPlanRepository {
  private collection: DBCollection<FootballMatchPlanDocument>;
  private readonly logger = new Logger(FootballMatchPlanRepository.name);

  constructor(@Inject(DATABASE_CONNECTION) private db: DBClient) {
    this.collection = db.collection(footballMatchPlanCollection);
    this.createIndexes();
  }

  private async createIndexes() {
    try {
      await this.collection.createIndex({ id: 1 }, { unique: true });
      await this.collection.createIndex({ teamId: 1 });
      await this.collection.createIndex({ teamId: 1, organizationId: 1, gameWeek: 1 });
    } catch (err) {
      this.logger.error(`Error creating indexes for ${this.constructor.name}`, err);
    }
  }

  //#region Commands

  async create(plan: FootballMatchPlan.CompletePlan): Promise<UnexpectedError | void> {
    return this.collection
      .insertOne({
        ...plan,
      })
      .then(() => undefined)
      .catch((err) => {
        // TODO: log
        return new UnexpectedError(err, {
          service: FootballMatchPlanRepository.name,
          method: this.create.name,
          operation: this.collection.insertOne.name,
        });
      });
  }

  async update(plan: FootballMatchPlan.CompletePlan) {
    const result = await this.collection
      .replaceOne({ id: plan.id }, plan)
      .then((res) => {
        if (res.modifiedCount < 1) {
          return new DomainError(ErrorMessages.EntityNotFound, {
            service: FootballMatchPlanRepository.name,
            method: this.update.name,
            operation: this.collection.replaceOne.name,
            plan: plan,
          });
        }
        if (res.modifiedCount > 1) {
          return new DomainError(ErrorMessages.MultipleEntitiesFound, {
            service: FootballMatchPlanRepository.name,
            method: this.update.name,
            operation: this.collection.replaceOne.name,
            plan: plan,
          });
        }

        return undefined;
      })
      .catch((err) => {
        return new UnexpectedError(err, {
          service: FootballMatchPlanRepository.name,
          method: this.update.name,
          operation: this.collection.replaceOne.name,
          plan: plan,
        });
      });

    if (isError(result)) {
      result.addContext({
        service: FootballMatchPlanRepository.name,
        method: this.update.name,
        operation: this.collection.replaceOne,
        payload: result,
      });

      return result;
    }

    return undefined;
  }

  //#endregion

  //#region Queries

  async getById(
    id: FootballMatchPlan.Id,
  ): Promise<UnexpectedError | ParsingError | FootballMatchPlan.CompletePlan | null> {
    return this.collection
      .findOne({ id })
      .then((plan) => {
        if (plan) {
          const parsed = FootballMatchPlan.Entity.toEntity(plan);
          if (isError(parsed)) {
            parsed.addContext({
              service: FootballMatchPlanRepository.name,
              method: this.getById.name,
              operation: FootballMatchPlan.Entity.toEntity.name,
              planId: id,
            });

            return parsed;
          }

          return parsed;
        }
        return null;
      })
      .catch((err) => {
        // TODO: log
        return new UnexpectedError(err, {
          service: FootballMatchPlanRepository.name,
          method: this.getById.name,
          operation: this.collection.findOne.name,
        });
      });
  }

  async getByTeamId(
    teamId: TeamId,
  ): Promise<UnexpectedError | ParsingError | FootballMatchPlan.CompletePlan[]> {
    return this.collection
      .find({ teamId })
      .toArray()
      .then((plans) => {
        if (plans) {
          const parsed = FootballMatchPlan.Entity.toEntities(plans);
          if (isError(parsed)) {
            parsed.addContext({
              service: FootballMatchPlanRepository.name,
              method: this.getByTeamId.name,
              operation: FootballMatchPlan.Entity.toEntities.name,
              teamId,
            });

            return parsed;
          }

          return parsed;
        }

        return [];
      })
      .catch((err) => {
        // TODO: log
        return new UnexpectedError(err, {
          service: FootballMatchPlanRepository.name,
          method: this.getByTeamId.name,
          operation: this.collection.find.name,
        });
      });
  }

  async getByGameWeek(
    teamId: TeamId,
    organizationId: OrganizationId,
    gameWeek: number,
    id?: FootballMatchPlan.Id,
  ) {
    return (
      this.collection
        // we use here an id when checking for the update action to avoid on update changing the matchWeek to an already existing matchWeek
        .findOne({ teamId, organizationId, gameWeek, id: id ? { $ne: id } : undefined })
        .then((plan) => {
          if (plan) {
            const parsed = FootballMatchPlan.Entity.toEntity(plan);

            if (isError(parsed)) {
              parsed.addContext({
                service: FootballMatchPlanRepository.name,
                method: this.getByGameWeek.name,
                operation: FootballMatchPlan.Entity.toEntities.name,
                gameWeek,
              });

              return parsed;
            }

            return parsed;
          }

          return null;
        })
        .catch((err) => {
          // TODO: log
          return new UnexpectedError(err, {
            service: FootballMatchPlanRepository.name,
            method: this.getByGameWeek.name,
            operation: this.collection.find.name,
            gameWeek,
          });
        })
    );
  }

  //#endregion
}
