import { Inject, Injectable, Logger } from "@nestjs/common";

import {
  Primitive,
  UnexpectedError,
  TeamId,
  ParsingError,
  isError,
  DomainError,
  ErrorMessages,
  FootballMatchAttemptAgainst,
  FootballMatchPlan,
  FootballMatchAttemptFor,
} from "@mio/helpers";

import { DATABASE_CONNECTION, DBClient, DBCollection } from "../../database";

type FootballMatchAttemptAgainstDocument = Primitive<FootballMatchAttemptAgainst.AttemptAgainst>;

export const footballMatchAttemptAgainstCollection = "football-match-attempts-against";

@Injectable()
export class FootballMatchAttemptAgainstRepository {
  private collection: DBCollection<FootballMatchAttemptAgainstDocument>;
  private readonly logger = new Logger(FootballMatchAttemptAgainstRepository.name);

  constructor(@Inject(DATABASE_CONNECTION) private db: DBClient) {
    this.collection = db.collection(footballMatchAttemptAgainstCollection);
    this.createIndexes();
  }

  private async createIndexes() {
    try {
      await this.collection.createIndex({ id: 1 }, { unique: true });
      await this.collection.createIndex({ teamId: 1, footballMatchPlanId: 1, type: 1 });
    } catch (err) {
      this.logger.error(`Error creating indexes for ${this.constructor.name}`, err);
    }
  }

  //#region Commands

  async create(plan: FootballMatchAttemptAgainst.AttemptAgainst): Promise<UnexpectedError | void> {
    return this.collection
      .insertOne({
        ...plan,
      })
      .then(() => undefined)
      .catch((err) => {
        // TODO: log
        return new UnexpectedError(err, {
          service: FootballMatchAttemptAgainstRepository.name,
          method: this.create.name,
          operation: this.collection.insertOne.name,
        });
      });
  }

  async update(plan: FootballMatchAttemptAgainst.AttemptAgainst) {
    const result = await this.collection
      .replaceOne({ id: plan.id }, plan)
      .then((res) => {
        if (res.modifiedCount < 1) {
          return new DomainError(ErrorMessages.EntityNotFound, {
            service: FootballMatchAttemptAgainstRepository.name,
            method: this.update.name,
            operation: this.collection.replaceOne.name,
            plan: plan,
          });
        }
        if (res.modifiedCount > 1) {
          return new DomainError(ErrorMessages.MultipleEntitiesFound, {
            service: FootballMatchAttemptAgainstRepository.name,
            method: this.update.name,
            operation: this.collection.replaceOne.name,
            plan: plan,
          });
        }

        return undefined;
      })
      .catch((err) => {
        return new UnexpectedError(err, {
          service: FootballMatchAttemptAgainstRepository.name,
          method: this.update.name,
          operation: this.collection.replaceOne.name,
          plan: plan,
        });
      });

    if (isError(result)) {
      result.addContext({
        service: FootballMatchAttemptAgainstRepository.name,
        method: this.update.name,
        operation: this.collection.replaceOne,
        payload: result,
      });

      return result;
    }

    return undefined;
  }

  async delete(id: FootballMatchAttemptAgainst.Id) {
    return this.collection
      .deleteOne({ id })
      .then((res) => {
        if (res.deletedCount < 1) {
          return new DomainError(ErrorMessages.EntityNotFound, {
            service: FootballMatchAttemptAgainstRepository.name,
            method: this.delete.name,
            operation: this.collection.deleteOne.name,
            planId: id,
          });
        }

        if (res.deletedCount > 1) {
          return new DomainError(ErrorMessages.MultipleEntitiesFound, {
            service: FootballMatchAttemptAgainstRepository.name,
            method: this.delete.name,
            operation: this.collection.deleteOne.name,
            planId: id,
          });
        }

        return undefined;
      })
      .catch((err) => {
        return new UnexpectedError(err, {
          service: FootballMatchAttemptAgainstRepository.name,
          method: this.delete.name,
          operation: this.collection.deleteOne.name,
          planId: id,
        });
      });
  }

  //#endregion

  //#region Queries

  async getById(
    id: FootballMatchAttemptAgainst.Id,
  ): Promise<UnexpectedError | ParsingError | FootballMatchAttemptAgainst.AttemptAgainst | null> {
    return this.collection
      .findOne({ id })
      .then((plan) => {
        if (plan) {
          const parsed = FootballMatchAttemptAgainst.Entity.toEntity(plan);
          if (isError(parsed)) {
            parsed.addContext({
              service: FootballMatchAttemptAgainstRepository.name,
              method: this.getById.name,
              operation: FootballMatchAttemptAgainst.Entity.toEntity.name,
              planId: id,
            });

            return parsed;
          }

          return parsed;
        }
        return null;
      })
      .catch((err) => {
        // TODO: log
        return new UnexpectedError(err, {
          service: FootballMatchAttemptAgainstRepository.name,
          method: this.getById.name,
          operation: this.collection.findOne.name,
        });
      });
  }

  async getByMatchPlanId(
    teamId: TeamId,
    footballMatchPlanId: FootballMatchPlan.Id,
    type: FootballMatchAttemptFor.AttemptForType | { $ne: FootballMatchAttemptFor.AttemptForType },
  ): Promise<UnexpectedError | ParsingError | FootballMatchAttemptAgainst.AttemptAgainst[]> {
    return this.collection
      .find({ teamId, footballMatchPlanId, type })
      .toArray()
      .then((plans) => {
        if (plans) {
          const parsed = FootballMatchAttemptAgainst.Entity.toEntities(plans);

          if (isError(parsed)) {
            parsed.addContext({
              service: FootballMatchAttemptAgainstRepository.name,
              method: this.getByMatchPlanId.name,
              operation: FootballMatchAttemptAgainst.Entity.toEntities.name,
              teamId,
            });

            return parsed;
          }

          return parsed;
        }

        return [];
      })
      .catch((err) => {
        // TODO: log
        return new UnexpectedError(err, {
          service: FootballMatchAttemptAgainstRepository.name,
          method: this.getByMatchPlanId.name,
          operation: this.collection.find.name,
        });
      });
  }

  async getAggregatedGoalsStats(teamId: TeamId) {
    return this.collection
      .aggregate([
        {
          $match: { teamId },
        },
        {
          $lookup: {
            from: "football-match-plans",
            localField: "footballMatchPlanId",
            foreignField: "id",
            pipeline: [
              {
                $project: {
                  _id: 0,
                  gameWeek: 1,
                },
              },
            ],
            as: "gameWeek",
          },
        },
        {
          $unwind: "$gameWeek",
        },
        {
          $set: {
            gameWeek: "$gameWeek.gameWeek",
          },
        },
      ])
      .toArray()
      .then((stats) => {
        if (stats && stats.length > 0) {
          const aggregatedStats =
            FootballMatchAttemptAgainst.Entity.toConcededGoalsWithGameWeek(stats);

          if (isError(aggregatedStats)) {
            aggregatedStats.addContext({
              service: FootballMatchAttemptAgainstRepository.name,
              method: this.getAggregatedGoalsStats.name,
              operation: FootballMatchAttemptAgainst.Entity.toConcededGoalsWithGameWeek.name,
              teamId,
            });

            return aggregatedStats;
          }

          const parsed = FootballMatchAttemptAgainst.Entity.aggregateTeamGoals(aggregatedStats);

          if (isError(parsed)) {
            parsed.addContext({
              service: FootballMatchAttemptAgainstRepository.name,
              method: this.getByMatchPlanId.name,
              operation: FootballMatchAttemptAgainst.Entity.toEntities.name,
              teamId,
            });

            return parsed;
          }

          return parsed;
        }

        return [];
      })
      .catch((err) => {
        return new UnexpectedError(err, {
          service: FootballMatchAttemptAgainstRepository.name,
          method: this.getAggregatedGoalsStats.name,
          operation: this.collection.aggregate.name,
        });
      });
  }

  //#endregion
}
