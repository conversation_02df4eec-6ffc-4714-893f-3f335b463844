import { Inject, Injectable, Logger } from "@nestjs/common";

import {
  DomainError,
  Email,
  ErrorMessages,
  Invite,
  InviteId,
  isError,
  OrganizationId,
  ParsingError,
  Primitive,
  UnexpectedError,
} from "@mio/helpers";
import { DATABASE_CONNECTION, DBClient, DBCollection } from "../database";

type InviteDocument = Primitive<Invite>;

export const invitesCollection = "invites";

@Injectable()
export class InviteRepository {
  private collection: DBCollection<InviteDocument>;
  private readonly logger = new Logger(InviteRepository.name);

  constructor(@Inject(DATABASE_CONNECTION) private db: DBClient) {
    this.collection = db.collection(invitesCollection);
    this.createIndexes();
  }

  private async createIndexes() {
    try {
      await this.collection.createIndex({ id: 1 }, { unique: true });
      await this.collection.createIndex({ organization: 1 });
      await this.collection.createIndex({ email: 1, organization: 1 });
    } catch (err) {
      this.logger.error(`Error creating indexes for ${this.constructor.name}`, err);
    }
  }

  async getByEmaildAndOrganization(
    email: Email,
    organization: OrganizationId,
  ): Promise<ParsingError | UnexpectedError | Invite | null> {
    return this.collection
      .findOne({ email, organization })
      .then((mbInvite) => {
        if (mbInvite) {
          const asEntity = Invite.toEntity(mbInvite);
          if (isError(asEntity)) {
            // TODO: log
          }

          return asEntity;
        }
        return null;
      })
      .catch((err) => {
        // TODO: log
        return new UnexpectedError(err);
      });
  }

  async getByOrganization(
    organization: OrganizationId,
  ): Promise<ParsingError | UnexpectedError | Invite[]> {
    return this.collection
      .find({ organization })
      .toArray()
      .then((invites) => {
        const entities = Invite.toEntities(invites);
        if (isError(entities)) {
          // TODO: log
        }

        return entities;
      })
      .catch((err) => {
        // TODO: log
        return new UnexpectedError(err);
      });
  }

  async inviteExists(id: InviteId): Promise<UnexpectedError | boolean> {
    return this.collection
      .countDocuments({ id })
      .then((result) => {
        return result > 0;
      })
      .catch((err) => new UnexpectedError(err));
  }

  async getById(id: InviteId): Promise<ParsingError | UnexpectedError | Invite | null> {
    return this.collection
      .findOne({ id })
      .then((mbInvite) => {
        if (mbInvite) {
          const asEntity = Invite.toEntity(mbInvite);
          if (isError(asEntity)) {
            // TODO: log
          }

          return asEntity;
        }
        return null;
      })
      .catch((err) => {
        // TODO: log
        return new UnexpectedError(err);
      });
  }

  async updateOne(invite: Invite) {
    return this.collection
      .updateOne({ id: invite.id }, { $set: invite })
      .then((res) => {
        if (res.matchedCount !== 1) {
          // TODO: log
          return new DomainError(ErrorMessages.RepositoryError);
        }
        return undefined;
      })
      .catch((err) => {
        // TODO: log
        return new UnexpectedError(err);
      });
  }

  async deleteOne(id: InviteId) {
    return this.collection
      .deleteOne({ id })
      .then((res) => {
        if (res.deletedCount < 1) {
          return new DomainError(ErrorMessages.EntityNotFound);
        }
        if (res.deletedCount > 1) {
          return new DomainError(ErrorMessages.MultipleEntitiesFound);
        }
        return undefined;
      })
      .catch((err) => {
        // TODO: log
        return new UnexpectedError(err);
      });
  }

  async create(invite: Invite): Promise<UnexpectedError | void> {
    return this.collection
      .insertOne({ ...invite })
      .then(() => undefined)
      .catch((err) => {
        // TODO: log
        return new UnexpectedError(err);
      });
  }
}
