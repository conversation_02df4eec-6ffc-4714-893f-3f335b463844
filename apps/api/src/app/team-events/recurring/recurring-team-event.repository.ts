import { Inject, Injectable, Logger } from "@nestjs/common";

import {
  Primitive,
  UnexpectedError,
  isError,
  DomainError,
  ErrorMessages,
  RecurringTeamEvent,
  TeamId,
  WithVersioning,
  CustomDate,
} from "@mio/helpers";

import { DATABASE_CONNECTION, DBClient, DBCollection } from "../../database";

type RecurringTeamEventDocument = Primitive<RecurringTeamEvent.RecurringTeamEvent> & WithVersioning;

export const recurringTeamEventsCollection = "recurring-team-events";

@Injectable()
export class RecurringTeamEventRepository {
  private collection: DBCollection<RecurringTeamEventDocument>;
  private readonly logger = new Logger(RecurringTeamEventRepository.name);

  constructor(@Inject(DATABASE_CONNECTION) private db: DBClient) {
    this.collection = db.collection(recurringTeamEventsCollection);
    this.createIndexes();
  }

  private async createIndexes() {
    try {
      await this.collection.createIndex({ id: 1 }, { unique: true });
      await this.collection.createIndex({ teamId: 1 });
    } catch (err) {
      this.logger.error(`Error creating indexes for ${this.constructor.name}`, err);
    }
  }

  async create(
    event: RecurringTeamEvent.RecurringTeamEvent,
    now = CustomDate.now(),
  ): Promise<UnexpectedError | void> {
    return this.collection
      .insertOne({ ...event, createdAt: now })
      .then(() => undefined)
      .catch((err) => {
        // TODO: log
        return new UnexpectedError(err);
      });
  }

  async update(event: RecurringTeamEvent.RecurringTeamEvent, now = CustomDate.now()) {
    return this.collection
      .updateOne({ id: event.id }, { $set: { ...event, updatedAt: now } })
      .then((result) => {
        if (isError(result)) {
          return result;
        }

        if (result.matchedCount < 1) {
          return new DomainError(ErrorMessages.EntityNotFound);
        }

        if (result.matchedCount > 1) {
          return new DomainError(ErrorMessages.MultipleEntitiesFound);
        }
      })
      .catch((err) => {
        // TODO: log
        return new UnexpectedError(err);
      });
  }

  async delete(eventId: RecurringTeamEvent.EventId) {
    return this.collection
      .deleteOne({
        id: eventId,
      })
      .then(() => undefined)
      .catch((err) => {
        // TODO: log
        return new UnexpectedError(err);
      });
  }

  async getById(id: RecurringTeamEvent.EventId) {
    return this.collection
      .findOne({ id })
      .then((mbEvent) => {
        if (mbEvent) {
          const asEntity = RecurringTeamEvent.Entity.toEntity(mbEvent);
          if (isError(asEntity)) {
            // TODO: log
          }

          return asEntity;
        }
        return null;
      })
      .catch((err) => {
        // TODO: log
        return new UnexpectedError(err);
      });
  }

  async getTeamEvents(teamId: TeamId) {
    return this.collection
      .find({ teamId })
      .toArray()
      .then((result) => {
        if (result) {
          const parsed = RecurringTeamEvent.Entity.toEntities(result);

          if (isError(parsed)) {
            parsed.addContext({
              service: RecurringTeamEventRepository.name,
              method: this.getTeamEvents.name,
              operation: RecurringTeamEvent.Entity.toEntities.name,
              data: result,
            });
          }

          return parsed;
        }

        return [];
      })
      .catch((err) => {
        return new UnexpectedError(err, {
          service: RecurringTeamEventRepository.name,
          method: this.getTeamEvents.name,
          operation: this.collection.find.name,
          teamId,
        });
      });
  }
}
