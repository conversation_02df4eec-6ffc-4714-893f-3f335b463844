import { Injectable } from "@nestjs/common";

import {
  isError,
  UnexpectedError,
  TeamId,
  RecurringTeamEvent,
  ParsingError,
  TeamEvent,
  EventScheduling,
  ValidDate,
  DomainError,
  ErrorMessages,
  PlayerId,
  CustomError,
} from "@mio/helpers";

import { TeamEventRepository } from "./team-event.repository";
import { SeasonService } from "../../season/season.service";

@Injectable()
export class TeamEventService {
  constructor(private repo: TeamEventRepository, private seasonService: SeasonService) {}

  async updatePlayerAttendance(
    eventId: TeamEvent.EventId,
    playerId: PlayerId,
    dto: TeamEvent.AttendanceDto,
  ): Promise<CustomError | TeamEvent.PlayerTeamEvent> {
    const event = await this.repo.getEventById(eventId);

    if (isError(event)) {
      event.addContext({
        service: TeamEventService.name,
        method: this.updatePlayerAttendance.name,
        operation: this.repo.getEventById.name,
        params: { eventId, playerId, dto },
      });

      return event;
    }

    if (!event) {
      return new DomainError(ErrorMessages.EntityNotFound, {
        service: TeamEventService.name,
        method: this.updatePlayerAttendance.name,
        operation: this.repo.getEventById.name,
        params: { eventId, playerId, dto },
      });
    }

    const attendance = TeamEvent.Entity.createOrUpdateAttendance(event, playerId, dto);

    if (isError(attendance)) {
      attendance.addContext({
        service: TeamEventService.name,
        method: this.updatePlayerAttendance.name,
        operation: TeamEvent.Entity.createOrUpdateAttendance.name,
        params: { eventId, playerId, dto },
      });

      return attendance;
    }

    const updatedEvent = TeamEvent.Entity.updateEventAttendance(event, attendance);

    const dbResult = await this.repo.updateEvent(updatedEvent);

    if (isError(dbResult)) {
      dbResult.addContext({
        service: TeamEventService.name,
        method: this.updatePlayerAttendance.name,
        operation: this.repo.updateEvent,
        updatedEvent,
        params: { eventId, playerId, dto },
      });

      return dbResult;
    }

    const playerTeamEvent = await this.repo.getPlayerEvent(eventId);

    if (isError(playerTeamEvent)) {
      playerTeamEvent.addContext({
        service: TeamEventService.name,
        method: this.updatePlayerAttendance.name,
        operation: this.repo.getPlayerEvent.name,
        params: { eventId, playerId, dto },
      });

      return playerTeamEvent;
    }

    if (!playerTeamEvent) {
      return new DomainError(ErrorMessages.EntityNotFound, {
        service: TeamEventService.name,
        method: this.updatePlayerAttendance.name,
        operation: this.repo.getPlayerEvent.name,
        params: { eventId, playerId, dto },
      });
    }

    return playerTeamEvent;
  }

  async isPlayerInvitedToEvent(
    eventId: TeamEvent.EventId,
    playerId: PlayerId,
  ): Promise<CustomError | boolean> {
    const event = await this.repo.getExtendedEvent(eventId);

    if (isError(event)) {
      event.addContext({
        service: TeamEventService.name,
        method: this.isPlayerInvitedToEvent.name,
        operation: this.repo.getExtendedEvent.name,
        params: { eventId, playerId },
      });

      return event;
    }

    if (!event) {
      return new DomainError(ErrorMessages.EntityNotFound, {
        service: TeamEventService.name,
        method: this.isPlayerInvitedToEvent.name,
        operation: this.repo.getExtendedEvent.name,
        params: { eventId, playerId },
      });
    }

    return TeamEvent.Entity.playerIsInvitedToEvent(event, playerId);
  }

  async getExtendedTeamEvent(
    eventId: TeamEvent.EventId,
  ): Promise<CustomError | TeamEvent.ExtendedTeamEvent> {
    const event = await this.repo.getExtendedEvent(eventId);

    if (isError(event)) {
      event.addContext({
        service: TeamEventService.name,
        method: this.getExtendedTeamEvent.name,
        operation: this.repo.getExtendedEvent.name,
        params: { eventId },
      });

      return event;
    }

    if (!event) {
      return new DomainError(ErrorMessages.EntityNotFound, {
        service: TeamEventService.name,
        method: this.getExtendedTeamEvent.name,
        operation: this.repo.getExtendedEvent.name,
        params: { eventId },
      });
    }

    return event;
  }

  async getTeamExtendedEvents(
    teamId: TeamId,
    query: TeamEvent.QueryDto,
  ): Promise<UnexpectedError | ParsingError | TeamEvent.ExtendedTeamEvent[]> {
    const result = await this.repo.getTeamEvents(teamId, query);

    if (isError(result)) {
      result.addContext({
        service: TeamEventService.name,
        method: this.getTeamExtendedEvents.name,
        operation: this.repo.getTeamEvents.name,
        teamId,
        query,
      });

      return result;
    }

    return result;
  }

  async getPlayerTeamEvents(
    playerId: PlayerId,
    query: TeamEvent.QueryDto,
  ): Promise<UnexpectedError | ParsingError | TeamEvent.PlayerTeamEvent[]> {
    const result = await this.repo.getPlayerEvents(playerId, query);

    if (isError(result)) {
      result.addContext({
        service: TeamEventService.name,
        method: this.getPlayerTeamEvents.name,
        operation: this.repo.getTeamEvents.name,
        playerId,
        query,
      });

      return result;
    }

    return result;
  }

  async createSingleEvent(
    dto: TeamEvent.CreateSingleDto,
  ): Promise<UnexpectedError | TeamEvent.SingleTeamEvent> {
    const event = TeamEvent.Entity.createSingle(dto);

    const result = await this.repo.create(event);

    if (isError(result)) {
      result.addContext({
        service: TeamEventService.name,
        method: this.createSingleEvent.name,
        operation: this.repo.create.name,
        dto,
      });

      return result;
    }

    return event;
  }

  async createEventsFromSchedule(
    recurringEvent: RecurringTeamEvent.RecurringTeamEvent,
  ): Promise<UnexpectedError | DomainError | ParsingError | Array<TeamEvent.RecurringTeamEvent>> {
    const endDate = await this.getEventEndDate(recurringEvent);

    if (isError(endDate)) {
      endDate.addContext({
        service: TeamEventService.name,
        method: this.createEventsFromSchedule.name,
        operation: this.getEventEndDate.name,
        recurringEvent,
      });

      return endDate;
    }

    const dates =
      recurringEvent.schedule.type === EventScheduling.Type.Monthly
        ? RecurringTeamEvent.Entity.getMonthlyRecurringDates(
            recurringEvent.schedule.date,
            recurringEvent.schedule.startFrom,
            endDate,
          )
        : RecurringTeamEvent.Entity.getWeeklyRecurringDates(
            recurringEvent.schedule.type,
            recurringEvent.schedule.day,
            recurringEvent.schedule.startFrom,
            endDate,
          );

    const dtos = dates.map((date) => {
      return TeamEvent.Entity.createRecurring(recurringEvent, date);
    });

    const bulkInsert = await this.repo.createInBulk(dtos);

    if (isError(bulkInsert)) {
      bulkInsert.addContext({
        service: TeamEventService.name,
        method: this.createEventsFromSchedule.name,
        operation: this.repo.createInBulk.name,
        dtos,
      });

      return bulkInsert;
    }

    return dtos;
  }

  async getUnansweredEventsCount(
    playerId: PlayerId,
    dateRange: TeamEvent.QueryDto,
  ): Promise<CustomError | number> {
    const result = await this.repo.getUnansweredEventsCount(playerId, dateRange);

    if (isError(result)) {
      result.addContext({
        service: TeamEventService.name,
        method: this.getUnansweredEventsCount.name,
        operation: this.repo.getUnansweredEventsCount.name,
        playerId,
        dateRange,
      });

      return result;
    }

    return result;
  }

  private async getEventEndDate(
    recurringEvent: RecurringTeamEvent.RecurringTeamEvent,
  ): Promise<ValidDate | DomainError | UnexpectedError | ParsingError> {
    if (recurringEvent.schedule.duration.type === EventScheduling.EndCriteria.EndOfSeason) {
      const season = await this.seasonService.getById(recurringEvent.schedule.duration.seasonId);

      if (isError(season)) {
        season.addContext({
          service: TeamEventService.name,
          method: this.getEventEndDate.name,
          operation: this.seasonService.getById.name,
          recurringEvent,
        });

        return season;
      }

      if (!season) {
        return new DomainError(ErrorMessages.SeasonNotFound, {
          service: TeamEventService.name,
          method: this.getEventEndDate.name,
          operation: this.seasonService.getById.name,
          recurringEvent,
        });
      }

      return season.endDate;
    } else {
      return Promise.resolve(recurringEvent.schedule.duration.date);
    }
  }
}
