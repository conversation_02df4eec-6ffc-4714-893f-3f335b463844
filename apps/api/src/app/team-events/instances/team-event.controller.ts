import {
  Controller,
  Get,
  Param,
  Query,
  UseGuards,
  Post,
  UsePipes,
  Body,
  Patch,
} from "@nestjs/common";

import {
  apiUrls,
  isError,
  PermissionsModule,
  UrlParams,
  TeamId,
  TeamEvent,
  UnexpectedError,
  ErrorTypes,
  PlayerId,
} from "@mio/helpers";

import { IDParamPipe, ZodValidationPipe, toBadRequest, toInternalServerError } from "../../shared";
import { JwtAuthGuard } from "../../auth/jwt-auth.guard";
import { PermissionsGuard, RequiresPermissions } from "../../permissions";
import { TeamEventService } from "./team-event.service";
import { TeamEventsQueryPipe } from "./query.pipe";
import { PlayerUserGuard } from "../../player-users/player-user.guard";
import { PlayerManageGuard } from "../../player-users/player-manage.guard";
import { PlayerEventGuard } from "./player-event.guard";

@Controller()
export class TeamEventController {
  constructor(private teamEventService: TeamEventService) {}

  @Patch(apiUrls.playerTeamEvent)
  /* 
  Guard checks:
  1. Make sure the target player is us
  2. Make sure the target player is actually invited to that particular event
  */
  @UseGuards(JwtAuthGuard, PlayerUserGuard, PlayerManageGuard, PlayerEventGuard)
  @UsePipes(new ZodValidationPipe(TeamEvent.Entity.attendanceDtoParser))
  async updatePlayerAttendance(
    @Body() dto: TeamEvent.AttendanceDto,
    @Param(UrlParams.TeamEventId, IDParamPipe) eventId: TeamEvent.EventId,
    @Param(UrlParams.PlayerId, IDParamPipe) playerId: PlayerId,
  ): Promise<TeamEvent.PlayerTeamEvent> {
    const result = await this.teamEventService.updatePlayerAttendance(eventId, playerId, dto);

    if (isError(result)) {
      switch (result.type) {
        case ErrorTypes.UnexpectedError:
          throw toInternalServerError(result);
        case ErrorTypes.DomainError:
          throw toBadRequest(result);
        case ErrorTypes.ParsingError:
          throw toInternalServerError(result);
        default:
          throw toInternalServerError(result);
      }
    }

    return result;
  }

  @Post(apiUrls.teamEvents)
  @UseGuards(JwtAuthGuard, PermissionsGuard)
  @RequiresPermissions([PermissionsModule.Action.Actions.ManageEvents])
  @UsePipes(new ZodValidationPipe(TeamEvent.Entity.createSingleDtoParser))
  async createEvent(
    @Body() dto: TeamEvent.CreateSingleDto,
  ): Promise<void | TeamEvent.SingleTeamEvent> {
    const result = await this.teamEventService.createSingleEvent(dto);

    if (isError(result)) {
      switch (result.type) {
        case ErrorTypes.UnexpectedError:
          throw toInternalServerError(result);
        default:
          throw toInternalServerError(result);
      }
    }

    return result;
  }

  @Get(apiUrls.teamEvents)
  @RequiresPermissions([PermissionsModule.Action.Actions.ManageEvents])
  @UseGuards(JwtAuthGuard, PermissionsGuard)
  async getTeamEvents(
    @Param(UrlParams.TeamId, IDParamPipe) teamId: TeamId,
    @Query("", TeamEventsQueryPipe) query: TeamEvent.QueryDto,
  ): Promise<UnexpectedError | TeamEvent.ExtendedTeamEvent[]> {
    const result = await this.teamEventService.getTeamExtendedEvents(teamId, query);

    if (isError(result)) {
      throw toInternalServerError(result);
    }

    return result;
  }

  @Get(apiUrls.playerEvents)
  @UseGuards(JwtAuthGuard, PlayerUserGuard, PlayerManageGuard)
  async getPlayerEvents(
    @Param(UrlParams.PlayerId, IDParamPipe) playerId: PlayerId,
    @Query("", TeamEventsQueryPipe) query: TeamEvent.QueryDto,
  ): Promise<UnexpectedError | TeamEvent.PlayerTeamEvent[]> {
    const result = await this.teamEventService.getPlayerTeamEvents(playerId, query);

    if (isError(result)) {
      throw toInternalServerError(result);
    }

    return result;
  }

  @Get(apiUrls.playerEventsUnansweredCount)
  @UseGuards(JwtAuthGuard, PlayerUserGuard, PlayerManageGuard)
  async getUnansweredEventsCount(
    @Param(UrlParams.PlayerId, IDParamPipe) playerId: PlayerId,
    @Query("", TeamEventsQueryPipe) query: TeamEvent.QueryDto,
  ): Promise<TeamEvent.UnansweredEventsCount> {
    const result = await this.teamEventService.getUnansweredEventsCount(playerId, query);

    if (isError(result)) {
      throw toInternalServerError(result);
    }

    return TeamEvent.Entity.unansweredEventsCountParser.parse({ count: result });
  }
}
