import { Test, TestingModule } from "@nestjs/testing";
import { INestApplication, Logger } from "@nestjs/common";

import { AppModule } from "../app.module";
import { AppTestService } from "../test/test.service";
import { DatabaseModule } from "../database/database.module";
import { createInMemoryMongoClient } from "../../test/memory-mongo";
import { DATABASE_CLIENT, DATABASE_CONNECTION, DBClient } from "../database";
import { RemindersService } from "./reminders.service";

describe("RemindersService (e2e)", () => {
  let app: INestApplication;
  let testService: AppTestService;
  let mongoClient: DBClient;
  let remindersService: RemindersService;

  beforeAll(async () => {
    const fakeMongo = await createInMemoryMongoClient();
    mongoClient = fakeMongo.db;

    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [AppModule],
      providers: [],
    })
      .overrideModule(DatabaseModule)
      .useModule({
        module: class TestDatabaseModule {},
        imports: [],
        providers: [
          {
            provide: DATABASE_CONNECTION,
            useFactory: async () => fakeMongo.db,
          },
          {
            provide: DATABASE_CLIENT,
            useFactory: async () => fakeMongo.mongoClient,
          },
          {
            provide: Logger,
            useValue: new Logger(RemindersService.name),
          },
        ],
        exports: [DATABASE_CONNECTION, DATABASE_CLIENT],
      })
      .compile();

    app = moduleFixture.createNestApplication();
    await app.init();

    testService = new AppTestService(mongoClient);
    remindersService = moduleFixture.get<RemindersService>(RemindersService);

    if (!remindersService.logger) {
      remindersService.logger = new Logger(RemindersService.name);
    }
  });

  afterEach(async () => {
    // Clear all collections between test cases
    const collections = await mongoClient.collections();
    for (const collection of collections) {
      await collection.deleteMany({});
    }
  });

  afterAll(async () => {
    await app.close();
  });

  describe("handleReminders()", () => {
    it.only("should not send reminders when there are no events", async () => {
      // Call the method directly
      console.log(remindersService.logger);
      await remindersService.handleReminders();

      // Verify no emails were sent (this would be checked in the email service mock)
      // For now, we'll just verify the method completes without error
    });

    it("should not send reminders when all players have responded", async () => {
      // Create a team event with a player who has already responded
      const { organization, profile: coachProfile } = await testService.getOrgWithCoach();
      const { team, applicants } = await testService.createTeamWithPlayers({
        organizationId: organization.id,
        applicantsCount: 1,
        type: "adult",
      });

      const player = applicants[0];

      if (!player.email) {
        throw new Error("Player email is undefined");
      }

      const teamEvent = await testService.createTeamEvent({
        teamId: team.id,
        organizationId: team.organizationId,
        invitations: [player.id],
        coachId: coachProfile.id,
      });

      // Player responds to the event
      await testService.updateTeamEventResponse({
        teamEventId: teamEvent.id,
        playerId: player.id,
        status: "positive",
      });

      // Call the method directly
      await remindersService.handleReminders();

      // Verify no emails were sent
    });

    it("should send reminders to players who haven't responded", async () => {
      // Create a team event with a player who hasn't responded
      const { organization, profile: coachProfile } = await testService.getOrgWithCoach();
      const { team, applicants } = await testService.createTeamWithPlayers({
        organizationId: organization.id,
        applicantsCount: 1,
        type: "adult",
      });

      const player = applicants[0];

      if (!player.email) {
        throw new Error("Player email is undefined");
      }

      const teamEvent = await testService.createTeamEvent({
        teamId: team.id,
        organizationId: team.organizationId,
        invitations: [player.id],
        coachId: coachProfile.id,
      });

      // Call the method directly
      await remindersService.handleReminders();

      // Verify that the event was updated with reminder information
      const updatedEvent = await mongoClient.collection("teamEvents").findOne({ id: teamEvent.id });

      if (!updatedEvent) {
        throw new Error("Event not found in the database");
      }

      // Check that remindersSent was updated
      expect(updatedEvent.remindersSent).toHaveLength(1);
      expect(updatedEvent.remindersSent[0].playerId).toEqual(player.id);
    });
  });
});
