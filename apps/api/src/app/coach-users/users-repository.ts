import { Inject, Injectable, Logger } from "@nestjs/common";

import {
  AuthTypes,
  DomainError,
  Email,
  ErrorMessages,
  isError,
  ParsingError,
  Primitive,
  UnexpectedError,
  CoachUser,
  CoachUserId,
  UserTypes,
  Organization,
} from "@mio/helpers";

import { DATABASE_CONNECTION, DBClient, DBCollection } from "../database";
import { permissionsCollection } from "../permissions/permissions.repository";
import { profilesCollection } from "../profile/profile.repository";
import { invitesCollection } from "../invites/invite.repository";

type UserDocument = Primitive<CoachUser>;

const coachUsersCollection = "users";

@Injectable()
export class CoachUsersRepository {
  private collection: DBCollection<UserDocument>;

  private profilesCollection: DBCollection;

  private readonly logger = new Logger(CoachUsersRepository.name);

  constructor(@Inject(DATABASE_CONNECTION) private db: DBClient) {
    this.collection = db.collection(coachUsersCollection);
    this.profilesCollection = db.collection(profilesCollection);
    this.createIndexes();
  }

  private async createIndexes() {
    try {
      await this.collection.createIndex({ id: 1 }, { unique: true });
      await this.collection.createIndex({ "authentication.email": 1, type: 1 });
    } catch (err) {
      this.logger.error(`Error creating indexes for ${this.constructor.name}`, err);
    }
  }

  async update(user: CoachUser) {
    return this.collection
      .updateOne({ id: user.id }, { $set: user })
      .then((res) => {
        if (res.matchedCount !== 1) {
          // TODO: log
          return new DomainError(ErrorMessages.RepositoryError);
        }
        return undefined;
      })
      .catch((err) => {
        // TODO: log
        return new UnexpectedError(err);
      });
  }

  async getUserByEmail(email: Email): Promise<UnexpectedError | ParsingError | CoachUser | null> {
    return this.collection
      .findOne({
        type: UserTypes.Coach,
        "authentication.email": email,
      })
      .then((user) => {
        if (user) {
          const asEntity = CoachUser.toEntity(user);
          if (isError(asEntity)) {
            // TODO: log
          }

          return asEntity;
        }
        return null;
      })
      .catch((err) => {
        // TODO: log
        return new UnexpectedError(err);
      });
  }

  async getUserById(id: CoachUserId): Promise<UnexpectedError | ParsingError | CoachUser | null> {
    return this.collection
      .findOne({
        id,
      })
      .then((user) => {
        if (user) {
          const asEntity = CoachUser.toEntity(user);
          if (isError(asEntity)) {
            // TODO: log
          }

          return asEntity;
        }
        return null;
      })
      .catch((err) => {
        // TODO: log
        return new UnexpectedError(err);
      });
  }

  async createUser(user: CoachUser): Promise<UnexpectedError | void> {
    return this.collection
      .insertOne({
        id: user.id,
        type: user.type,
        authentication: {
          type: AuthTypes.Credentials,
          email: user.authentication.email,
          password: user.authentication.password,
        },
      })
      .then(() => undefined)
      .catch((err) => {
        // TODO: log
        return new UnexpectedError(err);
      });
  }

  async getUsersByIds(ids: CoachUserId[]): Promise<ParsingError | UnexpectedError | CoachUser[]> {
    return this.collection
      .find({ id: { $in: ids } })
      .toArray()
      .then((users) => {
        const entities = CoachUser.toEntities(users);

        if (isError(entities)) {
          // TODO: log
        }

        return entities;
      })
      .catch((err) => {
        // TODO: log
        return new UnexpectedError(err);
      });
  }

  async getOrganizationUsers(organization: Organization) {
    /*

    * Finding all users within an organization: *

    1. The entry point is Organization.members[]: Array<ProfileId>
    2. Then we associate each found Profile with the respective User
    3. Since we want the User to be the main returned object with fields like .profile, .invite and .permission attached to it as secondary info,
       we do the "$replaceRoot" operation so we switch from the shape "profile.user" to "user". Then we attach the additional fields to each "user"
    4. Besides the frequently used $lookup / $unwind operations, we have the $project and $filter steps which only populate user.permissions[] with
       permissions for the target organization and don't leak any from other organizations.

    */

    const stages = [
      { $match: { id: { $in: organization.members } } },
      {
        $lookup: {
          from: coachUsersCollection,
          localField: "user",
          foreignField: "id",
          as: "user",
        },
      },
      {
        $unwind: "$user",
      },
      { $replaceRoot: { newRoot: "$user" } },
      {
        $lookup: {
          from: profilesCollection,
          localField: "id",
          foreignField: "user",
          as: "profile",
        },
      },
      {
        $unwind: "$profile",
      },
      {
        $lookup: {
          from: invitesCollection,
          localField: "authentication.email",
          foreignField: "email",
          as: "invite",
        },
      },
      {
        $unwind: "$invite",
      },
      {
        $match: { "invite.organization": organization.id },
      },
      {
        $lookup: {
          from: permissionsCollection,
          localField: "profile.id",
          foreignField: "profileId",
          as: "permissions",
        },
      },
      {
        $project: {
          id: 1,
          type: 1,
          authentication: 1,
          profile: 1,
          invite: 1,
          permissions: {
            $filter: {
              input: "$permissions",
              as: "permission",
              cond: { $eq: ["$$permission.organizationId", organization.id] },
            },
          },
        },
      },
    ];

    const cursor = this.profilesCollection.aggregate(stages);

    return cursor
      .toArray()
      .then((results) => {
        const parsed = CoachUser.toPopulatedEntitiesFromUnknown(results);

        if (isError(parsed)) {
          parsed.addContext({
            service: CoachUsersRepository.name,
            method: this.getOrganizationUsers.name,
            operation: CoachUser.toPopulatedEntitiesFromUnknown.name,
            input: results,
          });
        }

        return parsed;
      })
      .catch((err) => {
        return new UnexpectedError(err, {
          service: CoachUsersRepository.name,
          method: this.getOrganizationUsers.name,
          operation: this.profilesCollection.aggregate,
          organizationId: organization.id,
        });
      });
  }
}
