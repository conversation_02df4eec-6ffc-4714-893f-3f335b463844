import { Injectable } from "@nestjs/common";
import { ConfigService } from "@nestjs/config";

import { PositiveInteger } from "@mio/helpers";
import { AppConfig } from "./validation";

@Injectable()
export class AppConfigService {
  constructor(private confService: ConfigService<AppConfig>) {}

  getAzureBlobStorageConnectionString() {
    return this.confService.get("AZURE_BLOB_STORAGE_CONNECTION_STRING");
  }

  getDbHost() {
    return this.confService.get("DB_HOST");
  }

  getSaltRounds() {
    return this.confService.get("CRYPTO_SALT_ROUNDS") || 10;
  }

  getJWTSecret() {
    return this.confService.get("JWT_SECRET");
  }

  getJWTExpirationTime() {
    return this.confService.get("JWT_EXPIRATION") || "48h";
  }

  getApiKey() {
    return this.confService.get("API_KEY");
  }

  getSentryKey() {
    return this.confService.get("SENTRY_KEY");
  }

  getResendApiKey() {
    return this.confService.get("RESEND_API_KEY");
  }

  areEmailsDisabled() {
    return String(this.confService.get("DISABLE_EMAILS")) === "true";
  }

  getDefaultEmail() {
    return this.confService.get("DEFAULT_EMAIL") || "<EMAIL>";
  }

  getCoachPortalUrl() {
    return this.confService.get("COACH_PORTAL_URL");
  }

  getPlayerPortalUrl() {
    return this.confService.get("PLAYER_PORTAL_URL");
  }

  getInviteDurationDays() {
    return (Number(this.confService.get("INVITE_DURATION_DAYS")) || 5) as PositiveInteger;
  }

  getEncryptionKey() {
    return this.confService.get("ENCRYPTION_KEY");
  }
}
