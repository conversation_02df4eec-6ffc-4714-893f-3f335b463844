import { Inject, Injectable, Logger } from "@nestjs/common";

import {
  DomainError,
  ErrorMessages,
  isError,
  Organization,
  OrganizationId,
  OrganizationSlug,
  ParsingError,
  Primitive,
  ProfileId,
  UnexpectedError,
} from "@mio/helpers";

import { DATABASE_CONNECTION, DBClient, DBCollection } from "../database";

type OrganizationDocument = Primitive<Organization>;

export const organizationsCollection = "organizations";

@Injectable()
export class OrganizationRepository {
  private collection: DBCollection<OrganizationDocument>;
  private readonly logger = new Logger(OrganizationRepository.name);

  constructor(@Inject(DATABASE_CONNECTION) private db: DBClient) {
    this.collection = db.collection(organizationsCollection);
    this.createIndexes();
  }

  private async createIndexes() {
    try {
      await this.collection.createIndex({ id: 1 }, { unique: true });
      await this.collection.createIndex({ slug: 1 }, { unique: true });
      await this.collection.createIndex({ members: 1 });
    } catch (err) {
      this.logger.error(`Error creating indexes for ${this.constructor.name}`, err);
    }
  }

  async getAllByProfile(id: ProfileId): Promise<Organization[] | UnexpectedError | ParsingError> {
    return this.collection
      .find({ members: id })
      .toArray()
      .then((mbOrganizations) => {
        if (mbOrganizations) {
          const asEntities = Organization.toEntities(mbOrganizations);
          if (isError(asEntities)) {
            // TODO: log
          }

          return asEntities;
        }
        return [];
      })
      .catch((err) => {
        // TODO: log
        return new UnexpectedError(err);
      });
  }

  async getById(id: OrganizationId): Promise<UnexpectedError | ParsingError | Organization | null> {
    return this.collection
      .findOne({ id })
      .then((mbOrganization) => {
        if (mbOrganization) {
          const asEntity = Organization.toEntity(mbOrganization);
          if (isError(asEntity)) {
            // TODO: log
          }

          return asEntity;
        }
        return null;
      })
      .catch((err) => {
        // TODO: log
        return new UnexpectedError(err);
      });
  }

  async getBySlug(
    slug: OrganizationSlug,
  ): Promise<UnexpectedError | ParsingError | Organization | null> {
    return this.collection
      .findOne({ slug })
      .then((mbOrganization) => {
        if (mbOrganization) {
          const asEntity = Organization.toEntity(mbOrganization);
          if (isError(asEntity)) {
            // TODO: log
          }

          return asEntity;
        }
        return null;
      })
      .catch((err) => {
        // TODO: log
        return new UnexpectedError(err);
      });
  }

  async createOrganization(organization: Organization): Promise<UnexpectedError | void> {
    return this.collection
      .insertOne({ ...organization })
      .then(() => undefined)
      .catch((err) => {
        // TODO: log
        return new UnexpectedError(err);
      });
  }

  async update(organization: Organization) {
    const result = await this.collection.updateOne({ id: organization.id }, { $set: organization });

    if (result.matchedCount < 1) {
      return new DomainError(ErrorMessages.EntityNotFound);
    }

    if (result.matchedCount > 1) {
      return new DomainError(ErrorMessages.MultipleEntitiesFound);
    }

    return undefined;
  }
}
