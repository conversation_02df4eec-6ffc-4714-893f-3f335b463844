import { Test, TestingModule } from "@nestjs/testing";

import { Organization, ParsingError, UnexpectedError, UUID } from "@mio/helpers";
import { DATABASE_CONNECTION } from "../database";
import { OrganizationRepository } from "./organization.repository";

describe(OrganizationRepository.name, () => {
  let repository: OrganizationRepository;

  describe(OrganizationRepository.prototype.createOrganization.name, () => {
    const validOrganization = Organization.parse({
      name: "Monster inc",
      displayName: "Monsters",
      slug: "monsters",
      id: UUID.generate(),
      members: [],
      contactEmail: "<EMAIL>",
      senderEmail: "<EMAIL>",
    });

    it("returns undefined on success", async () => {
      const module: TestingModule = await Test.createTestingModule({
        providers: [
          OrganizationRepository,
          {
            provide: DATABASE_CONNECTION,
            useValue: {
              collection: () => ({
                insertOne: async () => undefined,
                createIndex: async () => undefined,
              }),
            },
          },
        ],
      }).compile();

      repository = module.get<OrganizationRepository>(OrganizationRepository);

      const result = await repository.createOrganization(validOrganization);
      expect(result).toBeUndefined();
    });

    it("returns an UnexpectedError", async () => {
      const module: TestingModule = await Test.createTestingModule({
        providers: [
          OrganizationRepository,
          {
            provide: DATABASE_CONNECTION,
            useValue: {
              collection: () => ({
                insertOne: async () => Promise.reject(new Error("something went wrong")),
                createIndex: async () => undefined,
              }),
            },
          },
        ],
      }).compile();

      repository = module.get<OrganizationRepository>(OrganizationRepository);

      const result = await repository.createOrganization(validOrganization);
      expect(result).toBeInstanceOf(UnexpectedError);
    });
  });

  describe(OrganizationRepository.prototype.getById.name, () => {
    const validOrganization = Organization.parse({
      name: "Monster inc",
      displayName: "Monsters",
      id: UUID.generate(),
      slug: "monsters",
      members: [],
      contactEmail: "<EMAIL>",
      senderEmail: "<EMAIL>",
    });

    it("returns the Organization", async () => {
      const module: TestingModule = await Test.createTestingModule({
        providers: [
          OrganizationRepository,
          {
            provide: DATABASE_CONNECTION,
            useValue: {
              collection: () => ({
                findOne: async () => validOrganization,
                createIndex: async () => undefined,
              }),
            },
          },
        ],
      }).compile();

      repository = module.get<OrganizationRepository>(OrganizationRepository);

      const result = await repository.getById(validOrganization.id);
      expect(result).toEqual(validOrganization);
    });

    it("returns a ParsingError", async () => {
      const module: TestingModule = await Test.createTestingModule({
        providers: [
          OrganizationRepository,
          {
            provide: DATABASE_CONNECTION,
            useValue: {
              collection: () => ({
                findOne: async () => ({ id: "not a valid id" }),
                createIndex: async () => undefined,
              }),
            },
          },
        ],
      }).compile();

      repository = module.get<OrganizationRepository>(OrganizationRepository);

      const result = await repository.getById(validOrganization.id);
      expect(result).toBeInstanceOf(ParsingError);
    });

    it("returns null when the Organization is not found", async () => {
      const module: TestingModule = await Test.createTestingModule({
        providers: [
          OrganizationRepository,
          {
            provide: DATABASE_CONNECTION,
            useValue: {
              collection: () => ({
                findOne: async () => null,
                createIndex: async () => undefined,
              }),
            },
          },
        ],
      }).compile();

      repository = module.get<OrganizationRepository>(OrganizationRepository);

      const result = await repository.getById(validOrganization.id);
      expect(result).toBeNull();
    });

    it("handles unexpected errors", async () => {
      const module: TestingModule = await Test.createTestingModule({
        providers: [
          OrganizationRepository,
          {
            provide: DATABASE_CONNECTION,
            useValue: {
              collection: () => ({
                findOne: async () => Promise.reject(new Error("oops")),
                createIndex: async () => undefined,
              }),
            },
          },
        ],
      }).compile();

      repository = module.get<OrganizationRepository>(OrganizationRepository);

      const result = await repository.getById(validOrganization.id);
      expect(result).toBeInstanceOf(UnexpectedError);
    });
  });
});
