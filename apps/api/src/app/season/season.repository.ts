import { Inject, Injectable, Logger } from "@nestjs/common";

import {
  Primitive,
  UnexpectedError,
  isError,
  Season,
  OrganizationId,
  SeasonId,
  DomainError,
  ErrorMessages,
  ValidDate,
  ParsingError,
} from "@mio/helpers";

import { DATABASE_CONNECTION, DBClient, DBCollection } from "../database";

type SeasonDocument = Primitive<Season>;

@Injectable()
export class SeasonRepository {
  private collection: DBCollection<SeasonDocument>;
  private readonly logger = new Logger(SeasonRepository.name);

  constructor(@Inject(DATABASE_CONNECTION) private db: DBClient) {
    this.collection = db.collection("seasons");
    this.createIndexes();
  }

  private async createIndexes() {
    try {
      await this.collection.createIndex({ id: 1 }, { unique: true });
      await this.collection.createIndex({ organizationId: 1 });
      await this.collection.createIndex({ endDate: 1 });
    } catch (err) {
      this.logger.error(`<PERSON>rror creating indexes for ${this.constructor.name}`, err);
    }
  }

  async create(season: Season): Promise<UnexpectedError | void> {
    return this.collection
      .insertOne({ ...season })
      .then(() => undefined)
      .catch((err) => {
        // TODO: log
        return new UnexpectedError(err);
      });
  }

  async update(season: Season) {
    const result = await this.collection
      .updateOne({ id: season.id }, { $set: season })
      .catch((err) => {
        // TODO: log
        return new UnexpectedError(err);
      });

    if (isError(result)) {
      return result;
    }

    if (result.matchedCount < 1) {
      return new DomainError(ErrorMessages.EntityNotFound);
    }

    if (result.matchedCount > 1) {
      return new DomainError(ErrorMessages.MultipleEntitiesFound);
    }

    return undefined;
  }

  async delete(seasonId: SeasonId) {
    return this.collection
      .deleteOne({
        id: seasonId,
      })
      .then(() => undefined)
      .catch((err) => {
        // TODO: log
        return new UnexpectedError(err);
      });
  }

  async findByOrganizationId(organizationId: OrganizationId) {
    return this.collection
      .find({ organizationId: organizationId })
      .toArray()
      .then((season) => {
        if (season) {
          const asEntity = Season.toEntities(season);
          if (isError(asEntity)) {
            // TODO: log
          }

          return asEntity;
        }
        return null;
      })
      .catch((err) => {
        // TODO: log
        return new UnexpectedError(err);
      });
  }

  async getById(id: SeasonId) {
    return this.collection
      .findOne({ id })
      .then((mbSeason) => {
        if (mbSeason) {
          const asEntity = Season.toEntity(mbSeason);
          if (isError(asEntity)) {
            // TODO: log
          }

          return asEntity;
        }
        return null;
      })
      .catch((err) => {
        // TODO: log
        return new UnexpectedError(err);
      });
  }

  async findByDate(date: ValidDate): Promise<ParsingError | UnexpectedError | null | Season> {
    return this.collection
      .findOne({ endDate: { $gte: date } })
      .then((result) => {
        if (result) {
          const parsed = Season.toEntity(result);

          if (isError(parsed)) {
            parsed.addContext({
              service: SeasonRepository.name,
              method: this.findByDate.name,
              operation: Season.toEntity.name,
              input: result,
              date,
            });
          }

          return parsed;
        }
        return null;
      })
      .catch((err) => {
        return new UnexpectedError(err, {
          service: SeasonRepository.name,
          method: this.findByDate.name,
          operation: this.collection.findOne.name,
          date,
        });
      });
  }
}
