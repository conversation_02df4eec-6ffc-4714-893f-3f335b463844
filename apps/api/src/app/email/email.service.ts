import { Injectable, Logger } from "@nestjs/common";
import { render } from "@react-email/render";
import { Resend } from "resend";
import { format } from "date-fns";

import {
  DomainError,
  Email,
  EmailDto,
  ErrorMessages,
  isError,
  OrganizationId,
  MarketingEmailDto,
  sharedUrls,
  UnexpectedError,
  UrlQuery,
  UserTypes,
  LoginCodeDto,
  TeamEvent,
  CustomDate,
} from "@mio/helpers";

import { SentryService } from "../../sentry/sentry.service";
import { AppConfigService } from "../config";
import { OrganizationSharedService } from "../organization/organization.shared.service";
import { TranslationsService, Locale } from "../translations/translations.service";

import { NewApplicantEmail } from "./templates/NewApplicantEmail";
import { PlayerLoginCodeEmail } from "./templates/PlayerLoginCodeEmail";
import { PasswordResetEmail } from "./templates/PasswordResetEmail";
import { GenericEmail } from "./templates/GenericEmail";
import { MarketingEmail } from "./templates/MarketingEmail";
import { EventReminderEmail } from "./templates/EventReminderEmail";

@Injectable()
export class EmailService {
  private readonly resend?: Resend;

  constructor(
    private appConfig: AppConfigService,
    private sentryService: SentryService,
    private orgService: OrganizationSharedService,
    private translationsService: TranslationsService,
  ) {
    if (!this.appConfig.areEmailsDisabled()) {
      this.resend = new Resend(this.appConfig.getResendApiKey());
    }
  }

  private readonly logger = new Logger(EmailService.name);

  async greetNewApplicant(email: Email, locale: Locale = "en") {
    const isSandboxed = this.appConfig.areEmailsDisabled();
    if (isSandboxed) {
      this.logger.log(`Email to ${email} was not sent because emails are disabled.`);
      return;
    }

    const clientUrl = this.appConfig.getPlayerPortalUrl();
    const subject = this.translationsService.translate("emails.newApplicant.subject", locale);

    const emailComponent = NewApplicantEmail({
      translationsService: this.translationsService,
      locale,
      clientUrl,
    });
    const html = await render(emailComponent);

    const msg = {
      to: email,
      from: this.appConfig.getDefaultEmail(),
      subject,
      html,
      text: "Please view this email in an HTML-compatible email client.",
    };

    try {
      await this.resend?.emails.send(msg);
      return undefined;
    } catch (error) {
      const exception = new UnexpectedError(error, {
        service: EmailService.name,
        method: this.greetNewApplicant.name,
        operation: this.resend?.emails.send.name,
        msg,
      });

      this.sentryService.logMessage(JSON.stringify(exception), exception);
      this.logger.error(JSON.stringify(exception));

      return exception;
    }
  }

  async sendPlayerLoginCode(params: LoginCodeDto, email: Email, locale: Locale = "en") {
    const isSandboxed = this.appConfig.areEmailsDisabled();
    if (isSandboxed) {
      this.logger.log(`Email to ${email} was not sent because emails are disabled.`);
      return;
    }

    const clientUrl = this.appConfig.getPlayerPortalUrl();
    const url = `${clientUrl}${sharedUrls.playerLoginWithCode}?${UrlQuery.code}=${params.code}`;
    const baseUrl = `${clientUrl}${sharedUrls.playerLoginWithCode}`;
    const subject = this.translationsService.translate("emails.playerLogin.subject", locale);

    const emailComponent = PlayerLoginCodeEmail({
      translationsService: this.translationsService,
      locale,
      url,
      baseUrl,
      code: params.code,
    });
    const html = await render(emailComponent);

    const msg = {
      to: email,
      from: this.appConfig.getDefaultEmail(),
      subject,
      html,
      text: "Please view this email in an HTML-compatible email client.",
    };

    try {
      await this.resend?.emails.send(msg);
      return undefined;
    } catch (error) {
      const exception = new UnexpectedError(error, {
        service: EmailService.name,
        method: this.sendPlayerLoginCode.name,
        operation: this.resend?.emails.send.name,
        msg,
      });
      this.sentryService.logMessage(JSON.stringify(exception), exception);
      this.logger.error(JSON.stringify(exception));

      return exception;
    }
  }

  async sendPasswordResetEmail(
    email: Email,
    code: string,
    userType: UserTypes,
    locale: Locale = "en",
  ) {
    const isSandboxed = this.appConfig.areEmailsDisabled();
    if (isSandboxed) {
      this.logger.log(`Email to ${email} was not sent because emails are disabled.`);
      return;
    }

    const logMessage = `Sending password reset email to ${email}`;
    this.sentryService.logMessage(logMessage);
    this.logger.log(logMessage);

    const clientUrl = this.appConfig.getCoachPortalUrl();
    const url = `${clientUrl}/${sharedUrls.passwordReset}?code=${code}&email=${email}&type=${userType}`;
    const subject = this.translationsService.translate("emails.passwordReset.subject", locale);

    const emailComponent = PasswordResetEmail({
      translationsService: this.translationsService,
      locale,
      url,
    });
    const html = await render(emailComponent);

    const msg = {
      to: email,
      from: this.appConfig.getDefaultEmail(),
      subject,
      html,
      text: "Please view this email in an HTML-compatible email client.",
    };

    try {
      const result = await this.resend?.emails.send(msg);
      return result;
    } catch (error) {
      const exception = new UnexpectedError(error, {
        service: EmailService.name,
        method: this.sendPasswordResetEmail.name,
        operation: this.resend?.emails.send.name,
        msg,
      });

      this.sentryService.logMessage(JSON.stringify(exception), exception);
      this.logger.error(JSON.stringify(exception));

      return exception;
    }
  }

  async sendEmailByOrganization(dto: EmailDto, orgId: OrganizationId) {
    const organization = await this.orgService.findById(orgId);

    if (isError(organization)) {
      organization.addContext({
        service: EmailService.name,
        method: this.sendEmailByOrganization.name,
        operation: this.orgService.findById.name,
        orgId,
        dto,
      });

      this.sentryService.logMessage(JSON.stringify(organization), organization);
      this.logger.error(JSON.stringify(organization));

      return organization;
    }

    if (!organization) {
      const exception = new DomainError(ErrorMessages.EntityNotFound, {
        service: EmailService.name,
        method: this.sendEmailByOrganization.name,
        operation: this.orgService.findById.name,
        orgId,
        dto,
      });

      this.sentryService.logMessage(JSON.stringify(exception), exception);
      this.logger.error(JSON.stringify(exception));

      return exception;
    }

    return this.sendEmail(dto, organization.contactEmail);
  }

  async sendEmail(dto: EmailDto, forwardEmail: Email) {
    const isSandboxed = this.appConfig.areEmailsDisabled();
    if (isSandboxed) {
      this.logger.log(`Email to ${dto.to.join()} was not sent because emails are disabled.`);
      return;
    }

    this.sentryService.logMessage(`Sending email to ${dto.to.join()}`);

    const emailComponent = GenericEmail({
      body: dto.body,
    });
    const html = await render(emailComponent);

    const msg = {
      to: forwardEmail,
      bcc: dto.to,
      from: "<EMAIL>",
      subject: dto.subject,
      html,
      text: "Please view this email in an HTML-compatible email client.",
    };

    try {
      const result = await this.resend?.emails.send(msg);

      return result;
    } catch (error) {
      const exception = new UnexpectedError(error, {
        service: EmailService.name,
        method: this.sendEmail.name,
        operation: this.resend?.emails.send.name,
        msg,
      });

      this.sentryService.logMessage(JSON.stringify(exception), exception);
      this.logger.error(JSON.stringify(exception));

      return exception;
    }
  }

  async sendMarketingEmail(dto: MarketingEmailDto, locale: Locale = "en") {
    const isSandboxed = this.appConfig.areEmailsDisabled();
    if (isSandboxed) {
      this.logger.log(`Marketing email to ${dto.email} was not sent because emails are disabled.`);
      return;
    }

    const subject = this.translationsService.translate("emails.marketing.subject", locale, {
      subject: dto.subject,
    });

    const emailComponent = MarketingEmail({
      translationsService: this.translationsService,
      locale,
      fullName: dto.fullName,
      email: dto.email,
      message: dto.message,
    });
    const html = await render(emailComponent);

    const msg = {
      to: ["<EMAIL>", "<EMAIL>"], // TODO: get via config
      from: "<EMAIL>",
      subject,
      html,
      text: "Please view this email in an HTML-compatible email client.",
    };

    try {
      const result = await this.resend?.emails.send(msg);
      return result;
    } catch (error) {
      return new UnexpectedError(error, {
        service: EmailService.name,
        method: this.sendMarketingEmail.name,
        operation: this.resend?.emails.send.name,
        dto,
      });
    }
  }

  async sendEventReminder(
    playerEmails: Email[],
    event: TeamEvent.TeamEvent,
    locale: Locale = "en",
  ) {
    const isSandboxed = this.appConfig.areEmailsDisabled();
    if (isSandboxed) {
      this.logger.log(`Email to ${playerEmails.join()} was not sent because emails are disabled.`);
      return;
    }

    const eventName = TeamEvent.Entity.getName(event as TeamEvent.ExtendedTeamEvent);
    const subject = this.translationsService.translate("emails.eventReminder.subject", locale, {
      eventName,
    });

    const clientUrl = this.appConfig.getPlayerPortalUrl();
    const eventUrl = `${clientUrl}/events/${event.id}`;

    const emailComponent = EventReminderEmail({
      translationsService: this.translationsService,
      locale,
      eventName,
      eventDate: format(event.startDateTime, "PPP"),
      eventTime: format(event.startDateTime, "p"),
      eventUrl,
    });

    const html = await render(emailComponent);

    const msg = {
      to: this.appConfig.getDefaultEmail(), // Send to a default address
      bcc: playerEmails, // BCC all the players
      from: this.appConfig.getDefaultEmail(),
      subject,
      html,
      text: "Please view this email in an HTML-compatible email client.",
    };

    try {
      await this.resend?.emails.send(msg);
      return undefined;
    } catch (error) {
      const exception = new UnexpectedError(error, {
        service: EmailService.name,
        method: this.sendEventReminder.name,
        operation: this.resend?.emails.send.name,
        msg,
      });

      this.sentryService.logMessage(JSON.stringify(exception), exception);
      this.logger.error(JSON.stringify(exception));

      return exception;
    }
  }
}
