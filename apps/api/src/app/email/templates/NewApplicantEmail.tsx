import { Body, Container, Head, Heading, Html, Text } from "@react-email/components";
import * as React from "react";
import { Locale, TranslationsService } from "../../translations/translations.service";

interface NewApplicantEmailProps {
  translationsService: TranslationsService;
  locale: Locale;
  clientUrl: string;
}

export const NewApplicantEmail = ({
  translationsService,
  locale,
  clientUrl,
}: NewApplicantEmailProps) => (
  <Html>
    <Head />
    <Body>
      <Container>
        <Heading>{translationsService.translate("emails.newApplicant.title", locale)}</Heading>
        <Text>
          {translationsService.translate("emails.newApplicant.body", locale, {
            url: clientUrl,
          })}
        </Text>
        <Text style={{ fontSize: "0.8em" }}>
          {translationsService.translate("emails.newApplicant.optOut", locale)}
        </Text>
      </Container>
    </Body>
  </Html>
);
