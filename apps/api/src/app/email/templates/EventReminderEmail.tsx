import { Body, Container, Head, Html, <PERSON>, <PERSON>, Button } from "@react-email/components";
import * as React from "react";
import { Locale, TranslationsService } from "../../translations/translations.service";

interface EventReminderEmailProps {
  translationsService: TranslationsService;
  locale: Locale;
  eventName: string;
  eventDate: string;
  eventTime: string;
  eventUrl: string;
}

export const EventReminderEmail = ({
  translationsService,
  locale,
  eventName,
  eventDate,
  eventTime,
  eventUrl,
}: EventReminderEmailProps) => (
  <Html>
    <Head />
    <Body style={main}>
      <Container style={container}>
        <Text style={title}>
          {translationsService.translate("emails.eventReminder.title", locale)}
        </Text>
        <Text style={paragraph}>
          {translationsService.translate("emails.eventReminder.greeting", locale)},
        </Text>
        <Text style={paragraph}>
          {translationsService.translate("emails.eventReminder.body", locale, {
            eventName,
            eventDate,
            eventTime,
          })}
        </Text>
        <Button style={button} href={eventUrl}>
          {translationsService.translate("emails.eventReminder.buttonText", locale)}
        </Button>
        <Text style={paragraph}>
          {translationsService.translate("emails.eventReminder.fallbackText", locale)}
        </Text>
        <Link href={eventUrl} style={link}>
          {eventUrl}
        </Link>
      </Container>
    </Body>
  </Html>
);

const main = {
  backgroundColor: "#f6f9fc",
  fontFamily:
    '-apple-system,BlinkMacSystemFont,"Segoe UI",Roboto,"Helvetica Neue",Ubuntu,sans-serif',
};

const container = {
  backgroundColor: "#ffffff",
  margin: "0 auto",
  padding: "20px 0 48px",
  marginBottom: "64px",
};

const title = {
  color: "#333",
  fontSize: "20px",
  fontWeight: "bold",
  lineHeight: "24px",
  padding: "0 20px",
};

const paragraph = {
  color: "#555",
  fontSize: "16px",
  lineHeight: "24px",
  textAlign: "left" as const,
  padding: "0 20px",
};

const button = {
  backgroundColor: "#5e6ad2",
  borderRadius: "5px",
  color: "#fff",
  fontSize: "16px",
  fontWeight: "bold",
  textDecoration: "none",
  textAlign: "center" as const,
  display: "block",
  width: "200px",
  padding: "12px",
  margin: "16px auto",
};

const link = {
  color: "#5e6ad2",
  textDecoration: "underline",
  fontSize: "14px",
  padding: "0 20px",
};
