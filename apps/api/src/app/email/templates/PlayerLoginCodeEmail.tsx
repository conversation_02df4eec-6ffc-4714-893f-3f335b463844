import { Body, Container, Head, Html, <PERSON>, Text } from "@react-email/components";
import * as React from "react";
import { Locale, TranslationsService } from "../../translations/translations.service";

interface PlayerLoginCodeEmailProps {
  translationsService: TranslationsService;
  locale: Locale;
  url: string;
  baseUrl: string;
  code: string;
}

export const PlayerLoginCodeEmail = ({
  translationsService,
  locale,
  url,
  baseUrl,
  code,
}: PlayerLoginCodeEmailProps) => (
  <Html>
    <Head />
    <Body>
      <Container>
        <Text>{translationsService.translate("emails.playerLogin.title", locale)}</Text>
        <Text>
          <Link href={url}>
            {translationsService.translate("emails.playerLogin.clickHere", locale, { url })}
          </Link>
          . {translationsService.translate("emails.playerLogin.validityNote", locale)}
        </Text>
        <Text>
          {translationsService.translate("emails.playerLogin.codeInstructions", locale, {
            code,
            baseUrl,
          })}
        </Text>
      </Container>
    </Body>
  </Html>
);
