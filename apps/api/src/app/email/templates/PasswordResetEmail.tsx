import { Body, Container, Head, Html, <PERSON>, Text } from "@react-email/components";
import * as React from "react";
import { Locale, TranslationsService } from "../../translations/translations.service";

interface PasswordResetEmailProps {
  translationsService: TranslationsService;
  locale: Locale;
  url: string;
}

export const PasswordResetEmail = ({
  translationsService,
  locale,
  url,
}: PasswordResetEmailProps) => (
  <Html>
    <Head />
    <Body>
      <Container>
        <Text>{translationsService.translate("emails.passwordReset.title", locale)}</Text>
        <Text>
          <Link href={url}>
            {translationsService.translate("emails.passwordReset.instructions", locale, { url })}
          </Link>
          . {translationsService.translate("emails.passwordReset.validityNote", locale)}
        </Text>
      </Container>
    </Body>
  </Html>
);
