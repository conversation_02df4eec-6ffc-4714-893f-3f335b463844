import { Body, Container, Head, Heading, Html, Text } from "@react-email/components";
import * as React from "react";
import { Locale, TranslationsService } from "../../translations/translations.service";

interface MarketingEmailProps {
  translationsService: TranslationsService;
  locale: Locale;
  fullName: string;
  email: string;
  message: string;
}

export const MarketingEmail = ({
  translationsService,
  locale,
  fullName,
  email,
  message,
}: MarketingEmailProps) => (
  <Html>
    <Head />
    <Body>
      <Container style={{ marginLeft: "20px", marginRight: "20px" }}>
        <Heading as="h3">
          {translationsService.translate("emails.marketing.title", locale, {
            fullName,
            email,
          })}
        </Heading>
        <div style={{ fontSize: "16px" }}>
          <Text>
            {translationsService.translate("emails.marketing.messageLabel", locale, {
              message,
            })}
          </Text>
          <br />
        </div>
      </Container>
    </Body>
  </Html>
);
