import { Inject, Injectable, Logger } from "@nestjs/common";

import {
  DomainError,
  ErrorMessages,
  isError,
  OrganizationId,
  ParsingError,
  PermissionsModule,
  Primitive,
  UnexpectedError,
} from "@mio/helpers";

import { DATABASE_CONNECTION, DBClient, DBCollection } from "../database";

type RoleDocument = Primitive<PermissionsModule.Role.Role>;

export const permissionRolesCollection = "permission-roles";

@Injectable()
export class RolesRepository {
  private collection: DBCollection<RoleDocument>;
  private readonly logger = new Logger(RolesRepository.name);

  constructor(@Inject(DATABASE_CONNECTION) private db: DBClient) {
    this.collection = db.collection(permissionRolesCollection);
    this.createIndexes();
  }

  private async createIndexes() {
    try {
      await this.collection.createIndex({ id: 1 }, { unique: true });
      await this.collection.createIndex({ organizationId: 1 });
      await this.collection.createIndex({ name: 1 }, { unique: true });
    } catch (err) {
      this.logger.error(`Error creating indexes for ${this.constructor.name}`, err);
    }
  }

  async getAllByOrgId(
    organizationId: OrganizationId,
  ): Promise<PermissionsModule.Role.Role[] | UnexpectedError | ParsingError> {
    return this.collection
      .find({ organizationId })
      .toArray()
      .then((results) => {
        if (results) {
          const asEntities = PermissionsModule.Role.Role.toEntities(results);

          if (isError(asEntities)) {
            // TODO: log
          }

          return asEntities;
        }
        return [];
      })
      .catch((err) => {
        // TODO: log
        return new UnexpectedError(err);
      });
  }

  async create(role: PermissionsModule.Role.Role): Promise<UnexpectedError | void> {
    return this.collection
      .insertOne({ ...role })
      .then(() => undefined)
      .catch((err) => {
        // TODO: log
        return new UnexpectedError(err);
      });
  }

  async update(role: PermissionsModule.Role.Role) {
    const result = await this.collection
      .updateOne({ id: role.id }, { $set: role })
      .catch((err) => new UnexpectedError(err));

    if (isError(result)) {
      return result;
    }

    if (result.matchedCount < 1) {
      return new DomainError(ErrorMessages.EntityNotFound);
    }

    if (result.matchedCount > 1) {
      return new DomainError(ErrorMessages.MultipleEntitiesFound);
    }

    return undefined;
  }

  async deleteOne(id: PermissionsModule.Role.RoleId) {
    return this.collection
      .deleteOne({ id })
      .then((res) => {
        if (res.deletedCount < 1) {
          return new DomainError(ErrorMessages.EntityNotFound);
        }
        if (res.deletedCount > 1) {
          return new DomainError(ErrorMessages.MultipleEntitiesFound);
        }
        return undefined;
      })
      .catch((err) => {
        // TODO: log
        return new UnexpectedError(err);
      });
  }

  async getById(
    id: PermissionsModule.Role.RoleId,
  ): Promise<UnexpectedError | ParsingError | PermissionsModule.Role.Role | null> {
    return this.collection
      .findOne({ id })
      .then((result) => {
        if (result) {
          const asEntity = PermissionsModule.Role.Role.toEntity(result);

          if (isError(asEntity)) {
            // TODO: log
          }

          return asEntity;
        }
        return null;
      })
      .catch((err) => {
        // TODO: log
        return new UnexpectedError(err);
      });
  }

  async getByName(
    name: PermissionsModule.Role.Role["name"],
  ): Promise<UnexpectedError | ParsingError | PermissionsModule.Role.Role | null> {
    return this.collection
      .findOne({ name })
      .then((result) => {
        if (result) {
          const asEntity = PermissionsModule.Role.Role.toEntity(result);

          if (isError(asEntity)) {
            // TODO: log
          }

          return asEntity;
        }
        return null;
      })
      .catch((err) => {
        // TODO: log
        return new UnexpectedError(err);
      });
  }
}
