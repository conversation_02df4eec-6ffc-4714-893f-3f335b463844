import { Inject, Injectable, Logger } from "@nestjs/common";

import {
  DomainError,
  ErrorMessages,
  isError,
  OrganizationId,
  ParsingError,
  PermissionsModule,
  Primitive,
  ProfileId,
  UnexpectedError,
} from "@mio/helpers";

import { DATABASE_CONNECTION, DBClient, DBCollection } from "../database";

export type PermissionDocument = Primitive<PermissionsModule.PermissionEntity.Permission>;

export const permissionsCollection = "permissions";

@Injectable()
export class PermissionRepository {
  private collection: DBCollection<PermissionDocument>;
  private readonly logger = new Logger(PermissionRepository.name);

  constructor(@Inject(DATABASE_CONNECTION) private db: DBClient) {
    this.collection = db.collection(permissionsCollection);
    this.createIndexes();
  }

  private async createIndexes() {
    try {
      await this.collection.createIndex({ id: 1 }, { unique: true });
      // Compound index covers all main query patterns:
      // - { profileId, organizationId }
      // - { profileId, organizationId, type }
      // - { profileId, roleId }
      await this.collection.createIndex({ profileId: 1, organizationId: 1, type: 1, roleId: 1 });
      await this.collection.createIndex({ organizationId: 1 });
    } catch (err) {
      this.logger.error(`Error creating indexes for ${this.constructor.name}`, err);
    }
  }

  async getAllByOrgId(
    organizationId: OrganizationId,
  ): Promise<PermissionsModule.PermissionEntity.Permission[] | UnexpectedError | ParsingError> {
    return this.collection
      .find({ organizationId })
      .toArray()
      .then((results) => {
        if (results) {
          const asEntities = PermissionsModule.PermissionEntity.Permission.toEntities(results);

          if (isError(asEntities)) {
            asEntities.addContext({
              service: PermissionRepository.name,
              method: this.getAllByOrgId.name,
              operation: this.collection.find.name,
              organizationId,
            });
          }

          return asEntities;
        }
        return [];
      })
      .catch((err) => {
        return new UnexpectedError(err, {
          service: PermissionRepository.name,
          method: this.getAllByOrgId.name,
          operation: this.collection.find.name,
          organizationId,
        });
      });
  }

  async getAllByProfileId(
    profileId: ProfileId,
    organizationId: OrganizationId,
  ): Promise<PermissionsModule.PermissionEntity.Permission[] | UnexpectedError | ParsingError> {
    return this.collection
      .find({ profileId, organizationId })
      .toArray()
      .then((results) => {
        if (results) {
          const asEntities = PermissionsModule.PermissionEntity.Permission.toEntities(results);

          if (isError(asEntities)) {
            asEntities.addContext({
              service: PermissionRepository.name,
              method: this.getAllByProfileId.name,
              operation: this.collection.find.name,
              profileId,
              organizationId,
            });
          }

          return asEntities;
        }
        return [];
      })
      .catch((err) => {
        return new UnexpectedError(err, {
          service: PermissionRepository.name,
          method: this.getAllByProfileId.name,
          operation: this.collection.find.name,
          profileId,
          organizationId,
        });
      });
  }

  async create(
    permission: PermissionsModule.PermissionEntity.Permission,
  ): Promise<UnexpectedError | void> {
    return this.collection
      .insertOne({ ...permission })
      .then(() => undefined)
      .catch((err) => {
        return new UnexpectedError(err, {
          service: PermissionRepository.name,
          method: this.create.name,
          operation: this.collection.insertOne.name,
          id: permission.id,
        });
      });
  }

  async update(permission: PermissionsModule.PermissionEntity.Permission) {
    const result = await this.collection
      .updateOne({ id: permission.id }, { $set: permission })
      .catch((err) => new UnexpectedError(err));

    if (isError(result)) {
      return result.addContext({
        service: PermissionRepository.name,
        method: this.update.name,
        operation: this.collection.updateOne.name,
        id: permission.id,
      });
    }

    if (result.matchedCount < 1) {
      return new DomainError(ErrorMessages.EntityNotFound, {
        service: PermissionRepository.name,
        method: this.update.name,
        operation: this.collection.updateOne.name,
        id: permission.id,
      });
    }

    if (result.matchedCount > 1) {
      return new DomainError(ErrorMessages.MultipleEntitiesFound, {
        service: PermissionRepository.name,
        method: this.update.name,
        operation: this.collection.updateOne.name,
        id: permission.id,
      });
    }

    return undefined;
  }

  async deleteOne(id: PermissionsModule.PermissionEntity.PermissionId) {
    return this.collection
      .deleteOne({ id })
      .then((res) => {
        if (res.deletedCount < 1) {
          return new DomainError(ErrorMessages.EntityNotFound, {
            service: PermissionRepository.name,
            method: this.deleteOne.name,
            operation: this.collection.deleteOne.name,
            id,
          });
        }
        if (res.deletedCount > 1) {
          return new DomainError(ErrorMessages.MultipleEntitiesFound, {
            service: PermissionRepository.name,
            method: this.deleteOne.name,
            operation: this.collection.deleteOne.name,
            id,
          });
        }
        return undefined;
      })
      .catch((err) => {
        return new UnexpectedError(err, {
          service: PermissionRepository.name,
          method: this.deleteOne.name,
          operation: this.collection.deleteOne.name,
          id,
        });
      });
  }

  async getById(
    id: PermissionsModule.PermissionEntity.PermissionId,
  ): Promise<
    UnexpectedError | ParsingError | PermissionsModule.PermissionEntity.Permission | null
  > {
    return this.collection
      .findOne({ id })
      .then((result) => {
        if (result) {
          const asEntity = PermissionsModule.PermissionEntity.Permission.toEntity(result);

          if (isError(asEntity)) {
            asEntity.addContext({
              service: PermissionRepository.name,
              method: this.getById.name,
              operation: PermissionsModule.PermissionEntity.Permission.toEntity.name,
              id,
            });
          }

          return asEntity;
        }
        return null;
      })
      .catch((err) => {
        return new UnexpectedError(err, {
          service: PermissionRepository.name,
          method: this.getById.name,
          operation: this.collection.findOne.name,
          id,
        });
      });
  }

  async getForProfileAndRole(
    profileId: ProfileId,
    roleId: PermissionsModule.Role.RoleId,
  ): Promise<
    UnexpectedError | ParsingError | PermissionsModule.PermissionEntity.Permission | null
  > {
    return this.collection
      .findOne({ profileId, roleId })
      .then((result) => {
        if (result) {
          const asEntity = PermissionsModule.PermissionEntity.Permission.toEntity(result);

          if (isError(asEntity)) {
            asEntity.addContext({
              service: PermissionRepository.name,
              method: this.getForProfileAndRole.name,
              operation: PermissionsModule.PermissionEntity.Permission.toEntity.name,
              profileId,
              roleId,
            });
          }

          return asEntity;
        }
        return null;
      })
      .catch((err) => {
        return new UnexpectedError(err, {
          service: PermissionRepository.name,
          method: this.getForProfileAndRole.name,
          operation: this.collection.findOne.name,
          profileId,
          roleId,
        });
      });
  }

  async getOwnerPermission(
    profileId: ProfileId,
    organizationId: OrganizationId,
  ): Promise<
    UnexpectedError | ParsingError | PermissionsModule.PermissionEntity.Permission | null
  > {
    return this.collection
      .findOne({ profileId, organizationId, type: PermissionsModule.PermissionEntity.Type.Owner })
      .then((result) => {
        if (result) {
          const asEntity = PermissionsModule.PermissionEntity.Permission.toEntity(result);

          if (isError(asEntity)) {
            asEntity.addContext({
              service: PermissionRepository.name,
              method: this.getOwnerPermission.name,
              operation: this.collection.findOne.name,
              profileId,
              organizationId,
            });
          }

          return asEntity;
        }
        return null;
      })
      .catch((err) => {
        return new UnexpectedError(err, {
          service: PermissionRepository.name,
          method: this.getOwnerPermission.name,
          operation: PermissionsModule.PermissionEntity.Permission.toEntity.name,
          profileId,
          organizationId,
        });
      });
  }
}
