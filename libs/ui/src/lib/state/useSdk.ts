import { useContext } from "react";
import axios from "axios";

import { AccessToken, DomainError, ErrorMessages } from "@mio/helpers";

import * as endpoints from "../sdk";
import { SDKContext } from "./contexts";

export const useSdk = () => {
  const sdk = useContext(SDKContext);

  if (!sdk) {
    throw new DomainError(ErrorMessages.SDKUninitialized);
  }

  return sdk;
};

type SDKParams = {
  baseURL: string;
  timeout?: number;
  getAccessToken: () => AccessToken;
  onUnauthorized: () => void;
};

export type SDK = ReturnType<typeof useSDKConfig>;

export const useSDKConfig = ({
  baseURL,
  onUnauthorized,
  // TODO: Remove this once we have a better way to handle timeouts
  timeout = 10000,
  getAccessToken,
}: SDKParams) => {
  const client = axios.create({
    baseURL,
    timeout,
  });

  client.interceptors.request.use((request) => {
    const existing = request.headers || {};
    const token = getAccessToken();

    // @ts-expect-error axios bug workaround, ref: https://github.com/axios/axios/issues/5034
    request.headers = { ...existing, Authorization: `Bearer ${token}` };
    return request;
  });

  client.interceptors.response.use(
    function (response) {
      // Any status code that lie within the range of 2xx cause this function to trigger
      // Do something with response data
      return response;
    },
    function (error) {
      if (error.response.status === 401) {
        onUnauthorized();
      }
      return Promise.reject(error);
    },
  );

  return {
    login: endpoints.makeLoginEndpoint(client),
    register: endpoints.makeRegisterEndpoint(client),
    getCurrentUser: endpoints.makeCurrentUserEndpoint(client),
    getOrganizationUsers: endpoints.makeGetOrganizationUsersEndpoint(client),
    getCurrentProfile: endpoints.makeGetProfileEndpoint(client),
    initiatePasswordReset: endpoints.makeInitiatePasswordReset(client),
    resetPassword: endpoints.makeResetPassword(client),
    getTeamCoaches: endpoints.makeGetTeamCoachesEndpoint(client),

    getInvite: endpoints.makeGetInviteEndpoint(client),
    getUnredeemedInvites: endpoints.makeGetUnredeemedInvites(client),
    deleteInvite: endpoints.makeDeleteInvite(client),
    createInvite: endpoints.makeCreateInvite(client),

    createRole: endpoints.makeCreateRole(client),
    updateRole: endpoints.makeUpdateRole(client),
    deleteRole: endpoints.makeDeleteRole(client),
    getRoles: endpoints.makeGetRoles(client),

    createPermission: endpoints.makeCreatePermission(client),
    updatePermission: endpoints.makeUpdatePermission(client),
    deletePermission: endpoints.makeDeletePermission(client),
    getCurrentPermissions: endpoints.makeGetPermissions(client),

    createTeam: endpoints.makeCreateTeam(client),
    updateTeam: endpoints.makeUpdateTeam(client),
    deleteTeam: endpoints.makeDeleteTeam(client),
    getTeams: endpoints.makeGetTeams(client),
    getAdminTeams: endpoints.makeGetAdminTeams(client),
    getTeam: endpoints.makeGetTeam(client),

    /* for first timers who don't have a Player entry yet */
    createApplicant: endpoints.makeCreateApplicant(client),
    /* for those who already have a Player entry in another Organization*/
    applyToOrganization: endpoints.makeApplyToOrganization(client),

    assignTeam: endpoints.makeAssignTeam(client),
    changeStatus: endpoints.makeChangeStatus(client),
    findPlayer: endpoints.makeFindPlayer(client),
    findOrganizationPlayer: endpoints.makeFindOrganizationPlayer(client),
    getPublicOrganization: endpoints.makeGetOrganizationBySlug(client),
    updateOrganization: endpoints.makeUpdateOrganization(client),
    joinOrganization: endpoints.makeJoinOrganization(client),
    queryPlayers: endpoints.makeQueryPlayers(client),
    getTeamPlayers: endpoints.makeGetTeamPlayers(client),
    removeFromTeam: endpoints.makeRemovePlayerFromTeam(client),
    sendEmail: endpoints.makeSendEmail(client),

    createSeason: endpoints.makeCreateSeason(client),
    getSeasons: endpoints.makeGetSeasons(client),
    updateSeason: endpoints.makeUpdateSeason(client),
    getSeason: endpoints.makeGetSeason(client),
    getCurrentSeason: endpoints.makeGetCurrentSeason(client),

    createRecurringTeamEvent: endpoints.makeCreateRecurringTeamEvent(client),
    updateRecurringTeamEvent: endpoints.makeUpdateRecurringTeamEvent(client),
    getTeamEvents: endpoints.makeGetTeamEvents(client),
    createTeamEvent: endpoints.makeCreateTeamEvent(client),

    getEmailTemplates: endpoints.makeGetEmailTemplates(client),
    createEmailTemplate: endpoints.makeCreateEmailTemplate(client),
    updateEmailTemplate: endpoints.makeUpdateEmailTemplate(client),
    deleteEmailTemplate: endpoints.makeDeleteEmailTemplate(client),

    player: {
      requestLoginCode: endpoints.playerAuth.makeRegisterEndpoint(client),
      loginWithCode: endpoints.playerAuth.makeLoginEndpoint(client),
      getCurrentUser: endpoints.playerUser.makeGetCurrentPlayerUser(client),

      removeFromOrganization: endpoints.makeRemovePlayerFromOrganization(client),

      getCurrentPlayers: endpoints.makeGetCurrentPlayers(client),
      getTeamEvents: endpoints.makeGetPlayerTeamEvents(client),
      respondToTeamEvent: endpoints.makeRespondToPlayerEvent(client),
      updatePlayerProfile: endpoints.makeUpdatePlayerProfile(client),

      uploadImage: endpoints.playerAssets.makeUploadImage(client),
      deleteImage: endpoints.playerAssets.makeDeleteImage(client),
      getImage: endpoints.playerAssets.makeGetImage(client),

      setPhoto: endpoints.makeSetPlayerPhoto(client),
      removePhoto: endpoints.makeRemovePlayerPhoto(client),
      uploadDocument: endpoints.makeUploadPlayerDocument(client),
      removeDocument: endpoints.makeRemovePlayerDocument(client),
      submitDocuments: endpoints.makeSubmitDocuments(client),
      getPlayerDocuments: endpoints.makeGetPlayerDocumentImages(client),
      getPlayerEventsUnansweredCount: endpoints.makeGetPlayerEventsUnansweredCount(client),
    },

    coach: {
      uploadImage: endpoints.coachAssets.makeUploadImageCloudOnly(client),
    },

    createFinancialIntegration: endpoints.makeCreateFinancialIntegration(client),
    updateFinancialIntegration: endpoints.makeUpdateFinancialIntegration(client),
    deleteFinancialIntegration: endpoints.makeDeleteFinancialIntegration(client),
    getFinancialIntegration: endpoints.makeGetFinancialIntegration(client),
    findFinancialIntegrations: endpoints.makeFindFinancialIntegrations(client),

    findGCSubscriptions: endpoints.makeGetGCSubscriptions(client),

    trainingSession: {
      createSeasonPlan: endpoints.trainingSession.makeCreateSeasonPlan(client),
      updateSeasonPlan: endpoints.trainingSession.makeUpdateSeasonPlan(client),
      getSeasonPlans: endpoints.trainingSession.makeGetSeasonPlans(client),

      createPractice: endpoints.trainingSession.makeCreatePractice(client),
      updatePractice: endpoints.trainingSession.makeUpdatePractice(client),
      getPractices: endpoints.trainingSession.makeGetPractices(client),

      createPlan: endpoints.trainingSession.makeCreatePlan(client),
      updatePlan: endpoints.trainingSession.makeUpdatePlan(client),
      getPlans: endpoints.trainingSession.makeGetPlans(client),

      createReview: endpoints.trainingSession.makeCreateReview(client),
      updateReview: endpoints.trainingSession.makeUpdateReview(client),
      getReviews: endpoints.trainingSession.makeGetReviews(client),
      getReview: endpoints.trainingSession.makeGetReview(client),

      createPlayerReview: endpoints.trainingSession.makeCreatePlayerReview(client),
      updatePlayerReview: endpoints.trainingSession.makeUpdatePlayerReview(client),
      getPlayerReviews: endpoints.trainingSession.makeGetPlayerReviews(client),
    },

    footballMatch: {
      createPlan: endpoints.footballMatch.makeCreatePlan(client),
      updatePlan: endpoints.footballMatch.makeUpdatePlan(client),
      getPlans: endpoints.footballMatch.makeGetPlans(client),
      getPlan: endpoints.footballMatch.makeGetPlan(client),

      createReview: endpoints.footballMatch.makeCreateReview(client),
      updateReview: endpoints.footballMatch.makeUpdateReview(client),
      getReviews: endpoints.footballMatch.makeGetReviews(client),
      getReview: endpoints.footballMatch.makeGetReview(client),

      upsertPlayerReviews: endpoints.footballMatch.makeUpsertPlayerReviews(client),
      updatePlayerReviews: endpoints.footballMatch.makeUpdatePlayerReviews(client),
      getPlayerReviews: endpoints.footballMatch.makeGetPlayerReviews(client),
      getAveragePlayerReviews: endpoints.footballMatch.makeGetAverageMatchPlayerReviews(client),

      createAttemptFor: endpoints.footballMatch.makeCreateAttemptFor(client),
      updateAttemptFor: endpoints.footballMatch.makeUpdateAttemptFor(client),
      getAttemptFor: endpoints.footballMatch.makeGetAttemptFor(client),
      getAttemptsFor: endpoints.footballMatch.makeGetAttemptsFor(client),
      getAttemptsForExtra: endpoints.footballMatch.makeGetExtraAttemptsFor(client),
      deleteAttemptFor: endpoints.footballMatch.makeDeleteAttemptFor(client),

      createAttemptAgainst: endpoints.footballMatch.makeCreateAttemptAgainst(client),
      updateAttemptAgainst: endpoints.footballMatch.makeUpdateAttemptAgainst(client),
      getAttemptAgainst: endpoints.footballMatch.makeGetAttemptAgainst(client),
      getAttemptsAgainst: endpoints.footballMatch.makeGetAttemptsAgainst(client),
      getAttemptsAgainstExtra: endpoints.footballMatch.makeGetExtraAttemptsAgainst(client),
      deleteAttemptAgainst: endpoints.footballMatch.makeDeleteAttemptAgainst(client),
    },

    footballMatchStats: {
      getMatchGoalStats: endpoints.footballMatchStats.makeGetPlayerMatchStats(client),
      getMatchTeamAttemptsForStats:
        endpoints.footballMatchStats.makeGetTeamAttemptsForStats(client),
      getMatchTeamAttemptsAgainstStats:
        endpoints.footballMatchStats.makeGetTeamAttemptsAgainstStats(client),
    },

    payments: {
      getPlayerPayments: endpoints.makeGetPlayerPayments(client),
      createCheckoutSession: endpoints.makeCreateCheckoutSession(client),
    },
  };
};
