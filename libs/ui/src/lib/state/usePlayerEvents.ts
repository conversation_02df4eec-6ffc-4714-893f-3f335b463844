import { useQuery } from "@tanstack/react-query";

import { APIError, PlayerId, TeamEvent, CustomNumber, must } from "@mio/helpers";
import { useSdk } from "./useSdk";

export const playerEventUnansweredKey = ["playerEventsUnanswered"];

export const usePlayerEventsUnansweredCount = (playerId?: PlayerId) => {
  const sdk = useSdk();

  // potentially this could become coach-configurable
  const initialDatePeriod = TeamEvent.Entity.constructFutureRange(
    CustomNumber.castToPositiveInteger(7),
  );

  const queryKey = [...playerEventUnansweredKey, playerId];

  return useQuery<TeamEvent.UnansweredEventsCount, APIError>(
    queryKey,
    () =>
      sdk.player.getPlayerEventsUnansweredCount({
        playerId: must(playerId),
        query: initialDatePeriod,
      }),
    {
      enabled: !!playerId,
    },
  );
};
