import { FC } from "react";
import { Box, Alert } from "@mui/material";

import { Button, TextField } from "../../../primitives";
import { useRequestForm, DataShape } from "./useForm";
import { UserTypes } from "@mio/helpers";

type Props = {
  onSubmit: (data: DataShape) => void;
  serverError?: string;
  isLoading?: boolean;
  success?: boolean;
  type: UserTypes;
};

const RequestBox: FC<Props> = ({ onSubmit, isLoading, serverError, type, success }) => {
  const { register, handleSubmit, errors } = useRequestForm(type);

  return (
    <Box component="form" sx={{ textAlign: "center" }} onSubmit={handleSubmit(onSubmit)}>
      <Box mb={3}>
        <TextField
          name="email"
          id="email"
          label="Email"
          autoComplete="on"
          type="email"
          fullWidth
          error={!!errors?.email?.message}
          helperText={errors?.email?.message || ""}
          disabled={isLoading}
          aria-invalid={!!errors?.email}
          inputProps={{ ...register("email") }}
        />
      </Box>

      {serverError && (
        <Box mb={2}>
          <Alert severity="error">{serverError}</Alert>
        </Box>
      )}

      {success && (
        <Box mb={2}>
          <Alert severity="success">
            We've sent an email to you. Check your inbox if you've given us an existing email.
          </Alert>
        </Box>
      )}

      <Box>
        <Button loading={isLoading} type="submit" variant="contained" size="large">
          Reset
        </Button>
      </Box>
    </Box>
  );
};

export { RequestBox, type Props as RequestBoxProps };
