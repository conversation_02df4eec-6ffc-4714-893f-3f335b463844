import { FC, useState } from "react";
import { Box, Alert } from "@mui/material";

import { Button, Requirements, SecretField, Stack } from "../../../primitives";
import { useUpdateForm, DataShape } from "./useForm";
import { Email, UserTypes } from "@mio/helpers";
import { usePasswordRequirements } from "../../register/usePasswordRequirements";

type Props = {
  onSubmit: (data: DataShape) => void;
  onPasswordToggle: (visiblePassword: boolean) => void;
  serverError?: string;
  isLoading?: boolean;
  success?: boolean;
  type: UserTypes;
  code: string;
  email: Email;
};

const UpdateBox: FC<Props> = ({
  onSubmit,
  isLoading,
  serverError,
  type,
  success,
  code,
  email,
  onPasswordToggle,
}) => {
  const { register, handleSubmit, passwordErrors, dirtyFields, newPassword } = useUpdateForm(
    type,
    code,
    email,
  );

  const [repeatedPassword, setRepeatedPassword] = useState("");
  const passwordMismatch = !!dirtyFields.newPassword && repeatedPassword !== newPassword;
  const requirements = usePasswordRequirements(passwordErrors);

  return (
    <Box component="form" sx={{ textAlign: "center" }} onSubmit={handleSubmit(onSubmit)}>
      <Box mb={2}>
        <SecretField
          fullWidth
          name="password"
          id="password"
          label="Password"
          error={dirtyFields.newPassword && passwordErrors.length > 0}
          disabled={isLoading}
          aria-invalid={!!passwordErrors.length}
          inputProps={{ ...register("newPassword") }}
          onToggle={onPasswordToggle}
        />
      </Box>

      <Box mb={2}>
        <Requirements
          value={newPassword}
          name={"New password"}
          requirements={requirements}
          readableLabelId="new-password"
        />
      </Box>

      <Box mb={2}>
        <SecretField
          fullWidth
          name="repeatedPassword"
          label="Repeat password"
          error={!!dirtyFields.newPassword && passwordMismatch}
          disabled={isLoading}
          aria-invalid={dirtyFields.newPassword && passwordMismatch}
          onChange={(event) => setRepeatedPassword(event.target.value)}
          helperText={passwordMismatch ? "Passwords don't match" : ""}
          onToggle={onPasswordToggle}
        />
      </Box>

      {serverError && (
        <Box mb={2}>
          <Alert severity="error">{serverError}</Alert>
        </Box>
      )}

      {success && (
        <Stack mb={2} gap={2}>
          <Alert severity="success">Password changed!</Alert>
        </Stack>
      )}

      <Box>
        <Button loading={isLoading} disabled={passwordMismatch} type="submit" variant="contained">
          Update
        </Button>
      </Box>
    </Box>
  );
};

export { UpdateBox, type Props as RequestBoxProps };
