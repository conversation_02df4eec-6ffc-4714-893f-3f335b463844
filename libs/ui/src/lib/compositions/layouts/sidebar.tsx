import { FC, PropsWithChildren, ReactNode, useEffect, useState } from "react";
import MUIAppBar from "@mui/material/AppBar";
import { styled, useTheme } from "@mui/material/styles";
import useMediaQuery from "@mui/material/useMediaQuery";

import {
  Box,
  CloseIcon,
  Container,
  Drawer,
  IconButton,
  MenuIcon,
  Stack,
  Toolbar,
} from "../../primitives";

const drawerWidth = 250;

type SecondaryProps = {
  open: boolean;
  temporaryMode: boolean;
};

type SidebarProps = PropsWithChildren & {
  content: ReactNode;
  title: ReactNode;
};

export const SidebarLayout: FC<SidebarProps> = ({ children, content, title }) => {
  const theme = useTheme();
  const isOnSmallScreen = useMediaQuery(theme.breakpoints.down("md"));
  const [open, setOpen] = useState(!isOnSmallScreen);

  useEffect(() => {
    if (isOnSmallScreen) {
      setOpen(false);
    }
  }, [isOnSmallScreen]);

  const handleDrawerOpen = () => {
    setOpen(true);
  };

  const handleDrawerClose = () => {
    setOpen(false);
  };

  return (
    <Box sx={{ display: "flex" }}>
      {(isOnSmallScreen || !open) && (
        <AppBar position="fixed" open={open} temporaryMode={isOnSmallScreen}>
          <Toolbar>
            <Stack direction="row" justifyContent={{ sm: "space-between" }} flexGrow={1}>
              <IconButton
                color="inherit"
                aria-label="open drawer"
                onClick={handleDrawerOpen}
                edge="start"
              >
                <MenuIcon />
              </IconButton>

              <Box ml={3}>{title}</Box>
            </Stack>
          </Toolbar>
        </AppBar>
      )}

      <Drawer
        sx={{
          width: drawerWidth,
          flexShrink: 0,
          "& .MuiDrawer-paper": {
            width: drawerWidth,
            boxSizing: "border-box",
            backgroundColor: theme.palette.primary.dark,
            color: theme.palette.primary.contrastText,
          },
        }}
        variant={isOnSmallScreen ? "temporary" : "persistent"}
        anchor="left"
        open={open}
      >
        <DrawerHeader>
          <Box ml={2}>{title}</Box>

          <IconButton onClick={handleDrawerClose}>
            <CloseIcon sx={{ color: theme.palette.primary.contrastText }} />
          </IconButton>
        </DrawerHeader>

        {content}
      </Drawer>

      <Main
        open={open}
        temporaryMode={isOnSmallScreen}
        sx={{ mt: open && !isOnSmallScreen ? 0 : 6 }}
      >
        <Container>{children}</Container>
      </Main>
    </Box>
  );
};

const Main = styled("main", {
  shouldForwardProp: (prop) => prop !== "open" && prop !== "temporaryMode",
})<SecondaryProps>(({ theme }) => {
  const isOnSmallScreen = useMediaQuery(theme.breakpoints.down("md"));

  return {
    flexGrow: 1,
    padding: theme.spacing(3),
    paddingLeft: theme.spacing(isOnSmallScreen ? 0 : 3),
    paddingRight: theme.spacing(isOnSmallScreen ? 0 : 3),
    transition: theme.transitions.create("margin", {
      easing: theme.transitions.easing.sharp,
      duration: theme.transitions.duration.leavingScreen,
    }),
    marginLeft: `-${drawerWidth}px`,
    variants: [
      {
        props: ({ temporaryMode }) => temporaryMode,
        style: {
          marginLeft: "initial",
        },
      },
      {
        props: ({ open }) => open,
        style: {
          transition: theme.transitions.create("margin", {
            easing: theme.transitions.easing.easeOut,
            duration: theme.transitions.duration.enteringScreen,
          }),
          marginLeft: 0,
        },
      },
    ],
  };
});

const AppBar = styled(MUIAppBar, {
  shouldForwardProp: (prop) => prop !== "open" && prop !== "temporaryMode",
})<SecondaryProps>(({ theme }) => ({
  transition: theme.transitions.create(["margin", "width"], {
    easing: theme.transitions.easing.sharp,
    duration: theme.transitions.duration.leavingScreen,
  }),
  variants: [
    {
      props: ({ temporaryMode, open }) => open && !temporaryMode,
      style: {
        width: `calc(100% - ${drawerWidth}px)`,
        marginLeft: `${drawerWidth}px`,
        transition: theme.transitions.create(["margin", "width"], {
          easing: theme.transitions.easing.easeOut,
          duration: theme.transitions.duration.enteringScreen,
        }),
      },
    },
  ],
}));

const DrawerHeader = styled("div")(({ theme }) => ({
  display: "flex",
  alignItems: "center",
  padding: theme.spacing(0, 1),
  // necessary for content to be below app bar
  ...theme.mixins.toolbar,
  justifyContent: "space-between",
}));
