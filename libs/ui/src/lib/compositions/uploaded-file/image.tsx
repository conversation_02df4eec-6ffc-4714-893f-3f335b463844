import { FC, ReactNode } from "react";
import { noop } from "lodash/fp";

import { APIError, Assets } from "@mio/helpers";

import { Stack, Box, Typography, DeleteIcon, Alert, Button } from "../../primitives";
import { useConfirmation } from "../confirmation";
import { ToggleSnackbar } from "../snackbar";

type Props = {
  image: Assets.Image.ImageAsset;
  altText: string;
  imageAction?: ReactNode;
  canRemove?: boolean;
  onRemove?: () => void;
  isRemoving?: boolean;
  apiError?: APIError;
};

export const UploadedImage: FC<Props> = ({
  image,
  altText,
  imageAction,
  onRemove,
  isRemoving,
  apiError,
  canRemove,
}) => {
  const { ConfirmationUI, open: confirmDelete } = useConfirmation({
    title: "Are you sure?",
    onConfirm: onRemove || noop,
    content: "Deleting the image might make it inaccessible to your coach.",
  });

  return (
    <Stack gap={2} direction="row" flexWrap="wrap">
      <Box
        component="img"
        src={image.url}
        alt={altText}
        maxWidth="100%"
        sx={{
          aspectRatio: "1 / 1",
          objectFit: "contain",
          backgroundColor: "grey.200",
        }}
      />
      <Stack gap={3}>
        {imageAction}
        {!imageAction && <Typography>{image.name}</Typography>}
        <Typography variant="subtitle2">{image.description}</Typography>
      </Stack>
      <Box justifySelf="flex-end">
        {canRemove && (
          <Button
            aria-label={`Remove image ${image.name}`}
            loading={isRemoving}
            startIcon={<DeleteIcon />}
            onClick={confirmDelete}
            variant="text"
          >
            Remove document
          </Button>
        )}
      </Box>

      <ToggleSnackbar open={!!apiError}>
        <Alert severity="error">Failed to remove image.</Alert>
      </ToggleSnackbar>

      {ConfirmationUI}
    </Stack>
  );
};
