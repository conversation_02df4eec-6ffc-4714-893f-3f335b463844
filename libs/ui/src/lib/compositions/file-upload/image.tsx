import { FC, useEffect, useState } from "react";

import { APIError, Assets, CustomFile, ParsingError, isError } from "@mio/helpers";

import {
  AttachFileIcon,
  Box,
  Button,
  CloseIcon,
  FormControl,
  FormHelperText,
  IconButton,
  Stack,
  Tooltip,
  Typography,
  UploadIcon,
} from "../../primitives";

type Props = {
  onUpload: (file: Assets.Image.ParsedFile) => void;
  isLoading: boolean;
  error?: APIError;
  success?: boolean;
};

export const ImageUpload: FC<Props> = ({ isLoading, onUpload, error: apiError, success }) => {
  const [file, setFile] = useState<Assets.Image.ParsedFile | undefined>();
  const [fileUrl, setFileUrl] = useState<string | undefined>();
  const [error, setError] = useState<ParsingError | undefined>();

  const handleFilePick = (data: unknown) => {
    const parsed = Assets.Image.Entity.toFile(data);

    if (isError(parsed)) {
      setFile(undefined);
      setError(parsed);
    } else {
      setError(undefined);
      setFile(parsed);
      setFileUrl(URL.createObjectURL(parsed));
    }
  };

  useEffect(() => {
    if (success) {
      setFile(undefined);
      setFileUrl(undefined);
      setError(undefined);
    }
  }, [success]);

  const handleFileUpload = (file: Assets.Image.ParsedFile) => {
    onUpload(file);
  };

  return (
    <FormControl>
      <Stack direction="row" justifyContent="space-between" alignItems="center" flexWrap="wrap">
        {!file && (
          <Button
            variant="outlined"
            component="label"
            startIcon={<AttachFileIcon />}
            loading={isLoading}
          >
            {isLoading ? (
              "Uploading..."
            ) : (
              <>
                Choose file
                <input
                  type="file"
                  accept={Assets.Image.fileTypes.join(",")}
                  hidden
                  onChange={(event) =>
                    handleFilePick(event.target.files ? event.target.files[0] : undefined)
                  }
                />
              </>
            )}
          </Button>
        )}

        {file && (
          <Stack gap={2}>
            <Stack direction="row" gap={1} alignItems="center" flexWrap="wrap">
              <Typography>{file.name}</Typography>

              {fileUrl && (
                <Box component="img" src={fileUrl} alt="Uploaded image" maxWidth="100%" />
              )}

              <Tooltip title="Remove file">
                <IconButton
                  onClick={() => {
                    setFile(undefined);
                    setFileUrl(undefined);
                  }}
                >
                  <CloseIcon />
                </IconButton>
              </Tooltip>
            </Stack>
            <Button
              variant="outlined"
              startIcon={<UploadIcon />}
              onClick={() => handleFileUpload(file)}
            >
              Upload
            </Button>
          </Stack>
        )}

        {error && <FormHelperText error={true}>{mapErrors(error.errors["file"])}</FormHelperText>}
        {apiError && <FormHelperText error={true}>Something went wrong.</FormHelperText>}
      </Stack>
    </FormControl>
  );
};

const mapErrors = (error: unknown): string => {
  switch (error) {
    case CustomFile.Errors.Required:
      return "File is required";
    case CustomFile.Errors.InvalidSize:
      return "File has invalid size.";
    case CustomFile.Errors.InvalidType:
      return "File has invalid type";
    case CustomFile.Errors.Invalid:
      return "Invalid file.";
    default:
      return "Invalid file.";
  }
};
