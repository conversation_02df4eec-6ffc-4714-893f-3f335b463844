import { FC } from "react";

import { Email } from "@mio/helpers";

import { <PERSON><PERSON>ield, TextField, Requirements, Box, Alert, Button } from "../../primitives";
import { useRegistrationForm, DataShape } from "./useRegisterForm";
import { usePasswordRequirements } from "./usePasswordRequirements";

export type Props = {
  onRegister: (data: DataShape) => void;
  onPasswordToggle: (passwordVisible: boolean) => void;
  email: Email;
  succeeded: boolean;
  serverError?: string;
  isLoading?: boolean;
};

const RegisterBox: FC<Props> = ({
  onRegister,
  isLoading,
  serverError,
  email,
  succeeded,
  onPasswordToggle,
}) => {
  const { register, handleSubmit, errors, dirtyFields, passwordErrors, password } =
    useRegistrationForm();

  const requirements = usePasswordRequirements(passwordErrors);

  return (
    <Box component="form" sx={{ textAlign: "center" }} onSubmit={handleSubmit(onRegister)}>
      <Box mb={1}>
        <TextField
          name="email"
          id="email"
          label="Email"
          type="email"
          value={email}
          fullWidth
          aria-readonly={true}
          disabled={true}
        />
      </Box>

      <Box mb={2}>
        <TextField
          fullWidth
          name="firstName"
          id="firstName"
          label="First name"
          error={dirtyFields.firstName && !!errors?.firstName?.message}
          helperText={dirtyFields.firstName ? errors?.firstName?.message : ""}
          disabled={isLoading}
          aria-invalid={!!errors?.firstName}
          inputProps={{ ...register("firstName") }}
        />
      </Box>

      <Box mb={2}>
        <TextField
          fullWidth
          name="lastName"
          id="lastName"
          label="Last name"
          error={dirtyFields.lastName && !!errors?.lastName?.message}
          helperText={dirtyFields.lastName ? errors?.lastName?.message : ""}
          disabled={isLoading}
          aria-invalid={!!errors?.lastName}
          inputProps={{ ...register("lastName") }}
        />
      </Box>

      <Box mb={2}>
        <SecretField
          fullWidth
          name="password"
          id="password"
          label="Password"
          error={dirtyFields.password && !!errors?.password?.message}
          helperText={dirtyFields.password ? errors?.password?.message : ""}
          disabled={isLoading}
          aria-invalid={!!errors?.password}
          inputProps={{ ...register("password") }}
          onToggle={onPasswordToggle}
        />
      </Box>

      <Box mb={2}>
        <Requirements
          value={password}
          name={"Password"}
          requirements={requirements}
          readableLabelId="password"
        />
      </Box>

      {serverError && (
        <Box mb={2}>
          <Alert severity="error">{serverError}</Alert>
        </Box>
      )}

      <Box mb={3}>
        {succeeded ? (
          <Alert severity="success">Registered successfully</Alert>
        ) : (
          <Button loading={isLoading} type="submit" variant="contained" size="large">
            Register
          </Button>
        )}
      </Box>
    </Box>
  );
};

export { RegisterBox, type Props as RegisterBoxProps };
