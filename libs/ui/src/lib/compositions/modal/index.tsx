import { FC, useState, useEffect, ReactNode } from "react";

import {
  Dialog,
  DialogProps,
  DialogTitle,
  DialogContent,
  DialogActions,
  Stack,
  IconButton,
  CloseIcon,
  Tooltip,
} from "../../primitives";

type Props = Omit<DialogProps, "title" | "content" | "footer" | "onClose"> & {
  open?: boolean;
  content?: ReactNode;
  footer?: ReactNode;
  onClose?: () => void;
  title?: ReactNode;
};

export const Modal: FC<Props> = ({ open, onClose, title, footer, content, ...rest }) => {
  const [isOpened, setOpened] = useState(!!open);

  useEffect(() => {
    setOpened(!!open);
  }, [open]);

  return (
    <Dialog
      {...rest}
      open={isOpened}
      onClose={(_event, reason) => {
        if (reason !== "backdropClick") {
          setOpened(false);
          if (onClose) {
            onClose();
          }
        }
      }}
      sx={{ ".MuiPaper-root": { overflow: "hidden" } }}
    >
      <DialogTitle>
        <Stack
          direction="row"
          justifyContent="space-between"
          alignItems="center"
          flexDirection="row-reverse"
        >
          <Tooltip title="Close modal">
            <IconButton
              aria-label="Close modal"
              onClick={() => {
                setOpened(false);
                if (onClose) {
                  onClose();
                }
              }}
            >
              <CloseIcon />
            </IconButton>
          </Tooltip>
          {title}
        </Stack>
      </DialogTitle>
      <DialogContent>{content}</DialogContent>
      <DialogActions>{footer}</DialogActions>
    </Dialog>
  );
};
