import { FC } from "react";
import { <PERSON>, Alert } from "@mui/material";

import { <PERSON><PERSON>, <PERSON>Field, TextField } from "../../primitives";
import { useLoginForm, DataShape } from "./useLoginForm";
import { UserTypes } from "@mio/helpers";

type Props = {
  onLogin: (data: DataShape) => void;
  onPasswordToggle?: (isVisible: boolean) => void;
  serverError?: string;
  isLoading?: boolean;
  type: UserTypes;
};

const LoginBox: FC<Props> = ({ onLogin, isLoading, serverError, type, onPasswordToggle }) => {
  const { register, handleSubmit, errors } = useLoginForm(type);

  return (
    <Box component="form" sx={{ textAlign: "center" }} onSubmit={handleSubmit(onLogin)}>
      <Box mb={3}>
        <TextField
          name="email"
          id="email"
          label="Email"
          autoComplete="on"
          type="email"
          fullWidth
          error={!!errors?.email?.message}
          helperText={errors?.email?.message || ""}
          disabled={isLoading}
          aria-invalid={!!errors?.email}
          inputProps={{ ...register("email") }}
        />
      </Box>

      <Box mb={2}>
        <SecretField
          fullWidth
          name="password"
          id="password"
          label="Password"
          error={!!errors?.password?.message}
          helperText={errors?.password?.message || ""}
          disabled={isLoading}
          aria-invalid={!!errors?.password}
          inputProps={{ ...register("password") }}
          onToggle={onPasswordToggle}
        />
      </Box>

      {serverError && (
        <Box mb={2}>
          <Alert severity="error">{serverError}</Alert>
        </Box>
      )}

      <Box>
        <Button loading={isLoading} type="submit" variant="contained" size="large">
          Login
        </Button>
      </Box>
    </Box>
  );
};

export { LoginBox, type Props as LoginBoxProps };
