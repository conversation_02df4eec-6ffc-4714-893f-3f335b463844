import { FC } from "react";

import Field from "../../../assets/playing-field.jpg";
import { Box, Stack } from "@mui/material";
import { visuallyHidden } from "../../utils/visuallyHidden";

type Props = {
  /**
   * Form field name
   */
  name: string;

  onChange: (selectedPosition: number) => void;

  /**
   * The selected position's number
   */
  value: number;

  /**
   * The background image of the football field
   */
  background?: string;

  /**
   * default: 6 columns, 3 rows
   */
  grid?: { columns: number; rows: number };
};

export const FieldWithZones: FC<Props> = ({
  name,
  value,
  onChange,
  background = Field,
  grid = { columns: 6, rows: 3 },
}) => {
  let counter = 1;
  const zones = Array.from({ length: grid.columns }).map(() =>
    Array.from({ length: grid.rows }).map(() => counter++),
  );

  return (
    <Box sx={{ position: "relative", display: "inline-flex" }}>
      <Box
        component="fieldset"
        sx={{
          position: "absolute",
          width: "100%",
          height: "100%",
          inset: 0,
          display: "grid",
          gridTemplateColumns: "repeat(var(--grid-columns), 1fr)",
          "--grid-columns": grid.columns,
        }}
      >
        <Box component="legend" sx={{ ...visuallyHidden }}>
          Field zones
        </Box>
        {zones.map((positions, index) => (
          <Stack direction="column" key={index}>
            {positions.map((position) => (
              <Stack
                key={position}
                direction="column"
                alignItems="center"
                component="label"
                justifyContent="center"
                sx={{
                  border: "1px solid black",
                  flexGrow: 1,
                  cursor: "pointer",
                  textDecoration: value === position ? "underline" : "none",
                  "@media (hover: hover) and (pointer: fine)": {
                    // the media query avoids hover on touch devices
                    "&:hover": {
                      opacity: 0.5,
                      background: "white",
                      "*": {
                        transform: "scale(1.3)",
                      },
                    },
                  },
                  "&:focus-within, &:has(input:checked)": {
                    opacity: 0.5,
                    background: "white",
                    "*": {
                      transform: "scale(1.3)",
                    },
                  },
                }}
              >
                <span>{position}</span>
                <Box
                  type="radio"
                  name={name}
                  value={position}
                  checked={value === position}
                  onChange={(event) => {
                    onChange(event.target.checked ? position : -1);
                  }}
                  component="input"
                  sx={{ ...visuallyHidden }}
                />
              </Stack>
            ))}
          </Stack>
        ))}
      </Box>
      <img src={background as string} alt="Soccer field" />
    </Box>
  );
};
