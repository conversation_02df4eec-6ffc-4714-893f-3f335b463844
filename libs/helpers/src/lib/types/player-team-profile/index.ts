import { omit } from "lodash/fp";
import { z } from "zod";
import { uniqueNamesGenerator, adjectives, animals } from "unique-names-generator";

import { zodToDomain } from "../../misc";
import { OrganizationId } from "../organization";
import { PlayerId } from "../player";
import { ProfileId } from "../profile";
import {
  UUID,
  ParsingError,
  CustomEnum,
  Tagged,
  CustomDate,
  ValidDate,
  Email,
  StringOfLength,
  Phone,
  CustomNumber,
} from "../shared";
import { TeamId } from "../team";
import { PlayerTeamStatus, PlayerTeamStatusWithTeam } from "./statuses";
import { genderParser } from "../shared/Gender";
import { PlayingExperience } from "../shared/PlayingExperience";
import { addressParser } from "../shared/Address";
import { embeddedGuardianParser } from "../player/Guardian";
import { documentsParser } from "../shared/Document";

export * from "./statuses";

export type PlayerTeamProfileId = Tagged<"PlayerTeamProfileId", UUID>;

const baseParser = z.object({
  organizationId: UUID.parser<OrganizationId>(),
  playerId: UUID.parser<PlayerId>(),

  firstName: StringOfLength.parser(1, 100).optional(),
  lastName: StringOfLength.parser(1, 100).optional(),
  email: Email.optionalParser,
  phone: Phone.parser.optional(),

  dob: CustomDate.dobParser(2, 60).optional(),
  medicalConditions: z.string().optional().nullable(),
  gender: genderParser.optional(),
  playingExperience: CustomEnum.parser(PlayingExperience).optional().nullable(),
  playingExperienceDescription: StringOfLength.parser(1, 300).optional().nullable(),
  address: addressParser.optional().nullable(),
  guardian: embeddedGuardianParser.optional().nullable(),
  documents: documentsParser.optional(),
  acceptedTerms: z.boolean().optional(),
});

const applicantParser = baseParser;

const standardParser = baseParser.merge(
  z.object({
    teamId: UUID.parser<TeamId>(),
  }),
);

const statusHistoryLogsParser = z.object({
  modifier: UUID.parser<ProfileId>().optional().nullable(),
  updatedAt: CustomDate.validParser(),
  newStatus: CustomEnum.parser(PlayerTeamStatus),
  newTeamId: UUID.parser<TeamId>().optional().nullable(),
});

const createParser = z.union([applicantParser, standardParser]);

const withId = z.object({
  id: UUID.parser<PlayerTeamProfileId>(),
});

const fullApplicantParser = applicantParser.merge(withId).merge(
  z.object({
    status: z.literal(PlayerTeamStatus.ApplicantOrganization),
    updatedAt: CustomDate.validParser(),
  }),
);

const fullStandardParser = standardParser.merge(withId).merge(
  z.object({
    status: CustomEnum.parser(PlayerTeamStatusWithTeam),
    statusHistoryLogs: z.array(statusHistoryLogsParser),
  }),
);

const fullParser = z.union([fullApplicantParser, fullStandardParser]);

export type CreateApplicantDto = z.infer<typeof applicantParser>;

export type CreatePlayerTeamProfileDto = z.infer<typeof createParser>;

export type CreateTeamApplicantDto = z.infer<typeof standardParser>;

export type CreatePlayerStatusHistoryLogDto = z.infer<typeof statusHistoryLogsParser>;

type ApplicantTeamProfile = z.infer<typeof fullApplicantParser>;
export type AssignedPlayerTeamProfile = z.infer<typeof fullStandardParser>;
export type PlayerTeamProfile = ApplicantTeamProfile | AssignedPlayerTeamProfile;

const assignTeamDtoParser = z.object({
  teamId: UUID.parser<TeamId>(),
  playerId: UUID.parser<PlayerId>(),
  organizationId: UUID.parser<OrganizationId>(),
});

export type AssignTeamDto = z.infer<typeof assignTeamDtoParser>;

const changeStatusDtoParser = z.object({
  teamId: UUID.parser<TeamId>(),
  playerId: UUID.parser<PlayerId>(),
  status: CustomEnum.parser(PlayerTeamStatusWithTeam),
  organizationId: UUID.parser<OrganizationId>(),
  startRegistrationProcess: z.boolean().optional(),
});

export type ChangeStatusDto = z.infer<typeof changeStatusDtoParser>;

export enum StatsCategory {
  Goals = "goals",
  Assists = "assists",
  KeyPasses = "key-passes",
}

export enum StatIndividualRole {
  Scorer = "scorer",
  Assist = "assist",
  KeyPass = "keyPass",
}

export const queryStatsCategory = z.object({
  category: CustomEnum.parser(StatsCategory),
});

export type StatsCategoryQueryDto = z.infer<typeof queryStatsCategory>;

export type StatsCategorySearchParams = {
  orgId: OrganizationId;
  teamId: TeamId;
  query: StatsCategoryQueryDto;
};

export const PlayerTeamProfile = {
  /* used by BE controllers */
  toCreateDto: createParser,
  fullParser,

  assignTeamDtoParser,
  toAssignTeamDto: (source: unknown) => zodToDomain(assignTeamDtoParser, source),

  changeStatusDtoParser,

  isOrganizationApplicant: (profile: PlayerTeamProfile): profile is ApplicantTeamProfile => {
    return profile.status === PlayerTeamStatus.ApplicantOrganization;
  },

  createApplicant: (
    dto: CreateApplicantDto,
    id = UUID.generate<PlayerTeamProfileId>(),
    now = CustomDate.now(),
  ): ApplicantTeamProfile => {
    return {
      ...dto,
      id,
      status: PlayerTeamStatus.ApplicantOrganization,
      updatedAt: now,
    };
  },

  updatePersonalInfo: (
    existingData: PlayerTeamProfile,
    newData: Omit<PlayerTeamProfile, "organizationId" | "playerId" | "status" | "id">,
  ): PlayerTeamProfile => {
    return {
      ...existingData,
      firstName: newData.firstName,
      lastName: newData.lastName,
      email: newData.email,
      phone: newData.phone,
      dob: newData.dob,
      medicalConditions: newData.medicalConditions,
      gender: newData.gender,
      playingExperience: newData.playingExperience,
      playingExperienceDescription: newData.playingExperienceDescription,
      address: newData.address,
      guardian: newData.guardian,
      documents: newData.documents,
    };
  },

  removeTeam: (
    data: { dto: AssignedPlayerTeamProfile[]; modifier: ProfileId },
    now = CustomDate.now(),
    status = PlayerTeamStatus.ApplicantOrganization,
  ): PlayerTeamProfile[] => {
    return data.dto.map((playah) => ({
      ...omit(["teamId", "statusHistoryLogs"], playah),
      status,
      updatedAt: now,
    }));
  },

  anonymizeNames: () => {
    return {
      firstName: "Removed",
      lastName: `Player ${uniqueNamesGenerator({
        dictionaries: [adjectives, animals],
        separator: "",
      })}`,
    };
  },

  assignNewTeam: (data: {
    profile: ApplicantTeamProfile;
    modifier: ProfileId;
    teamId: TeamId;
    status: PlayerTeamStatusWithTeam;
    now: ValidDate;
  }): AssignedPlayerTeamProfile => {
    return {
      ...omit("updatedAt", data.profile),
      status: data.status,
      teamId: data.teamId,
      statusHistoryLogs: [
        {
          updatedAt: data.profile.updatedAt,
          newStatus: PlayerTeamStatus.ApplicantOrganization,
        },
        {
          updatedAt: data.now,
          newStatus: data.status,
          modifier: data.modifier,
          newTeamId: data.teamId,
        },
      ],
    };
  },

  changeStatus: (data: {
    profile: AssignedPlayerTeamProfile;
    modifier: ProfileId;
    teamId: TeamId;
    status: PlayerTeamStatusWithTeam;
    now: ValidDate;
  }): AssignedPlayerTeamProfile => {
    return {
      status: data.status,
      teamId: data.teamId,
      organizationId: data.profile.organizationId,
      playerId: data.profile.playerId,
      id: data.profile.id,
      firstName: data.profile.firstName,
      lastName: data.profile.lastName,
      email: data.profile.email,
      phone: data.profile.phone,
      dob: data.profile.dob,
      medicalConditions: data.profile.medicalConditions,
      gender: data.profile.gender,
      playingExperience: data.profile.playingExperience,
      playingExperienceDescription: data.profile.playingExperienceDescription,
      address: data.profile.address,
      guardian: data.profile.guardian,
      documents: data.profile.documents,
      acceptedTerms: data.profile.acceptedTerms,
      statusHistoryLogs: [
        ...data.profile.statusHistoryLogs,
        {
          updatedAt: data.now,
          newStatus: data.status,
          modifier: data.modifier,
          newTeamId: null,
        },
      ],
    };
  },

  deletePlayersTeamProfiles: (data: {
    profiles: AssignedPlayerTeamProfile[];
    modifier: ProfileId;
    now: ValidDate;
  }): AssignedPlayerTeamProfile[] => {
    const res = data.profiles.map((profile) => ({
      id: UUID.generate<PlayerTeamProfileId>(),
      playerTeamProfileId: profile.id,
      organizationId: profile.organizationId,
      playerId: profile.playerId,
      status: profile.status,
      teamId: profile.teamId,
      statusHistoryLogs: [
        ...profile.statusHistoryLogs,
        {
          updatedAt: data.now,
          modifier: data.modifier,
          newStatus: PlayerTeamStatus.RemovedTeam,
        },
      ],
    }));

    return res;
  },

  /* used in the FE form */
  parseCreateDto: (source: unknown) => {
    const result = createParser.safeParse(source);

    return result.success ? result.data : new ParsingError(result.error);
  },

  toEntity: (source: unknown) => zodToDomain(fullParser, source),

  toAssignedPlayerEntity: (source: unknown) => {
    const result = fullStandardParser.safeParse(source);

    return result.success ? result.data : new ParsingError(result.error);
  },

  toManyAssignedPlayerEntities: (source: unknown) => {
    const result = z.array(fullStandardParser).safeParse(source);

    if (result.success) {
      return result.data;
    }

    return new ParsingError(result.error);
  },

  toManyEntities: (source: unknown) => zodToDomain(z.array(fullParser), source),

  toDemoInstance: (dto?: Partial<AssignedPlayerTeamProfile>) => {
    return {
      id: UUID.generate<PlayerTeamProfileId>(),
      playerId: UUID.generate<PlayerId>(),
      organizationId: UUID.generate<OrganizationId>(),
      teamId: UUID.generate<TeamId>(),
      statusHistoryLogs: [],
      status: PlayerTeamStatus.RegisteredMatches,
      firstName: baseParser.shape.firstName.parse("John"),
      lastName: baseParser.shape.lastName.parse("Doe"),
      email: baseParser.shape.email.parse("<EMAIL>"),
      phone: baseParser.shape.phone.parse("**********"),
      dob: baseParser.shape.dob.parse(CustomDate.subYears(CustomNumber.toPositiveInteger(20))),
      medicalConditions: "None",
      guardian: embeddedGuardianParser.parse({
        firstName: "Jane",
        lastName: "Doe",
        email: "<EMAIL>",
        phone: "**********",
        dob: CustomDate.subYears(CustomNumber.toPositiveInteger(50)),
      }),
      ...dto,
    } as const satisfies AssignedPlayerTeamProfile;
  },

  toApplicantDemoInstance: (dto?: Partial<ApplicantTeamProfile>): ApplicantTeamProfile => {
    return {
      id: UUID.generate<PlayerTeamProfileId>(),
      playerId: UUID.generate<PlayerId>(),
      organizationId: UUID.generate<OrganizationId>(),
      status: PlayerTeamStatus.ApplicantOrganization,
      updatedAt: CustomDate.now(),
      firstName: baseParser.shape.firstName.parse("John"),
      lastName: baseParser.shape.lastName.parse("Doe"),
      email: baseParser.shape.email.parse("<EMAIL>"),
      phone: baseParser.shape.phone.parse("**********"),
      dob: baseParser.shape.dob.parse(CustomDate.subYears(CustomNumber.toPositiveInteger(20))),
      medicalConditions: "None",
      guardian: embeddedGuardianParser.parse({
        firstName: "Jane",
        lastName: "Doe",
        email: "<EMAIL>",
        phone: "**********",
        dob: CustomDate.subYears(CustomNumber.toPositiveInteger(50)),
      }),
      ...dto,
    } as const satisfies ApplicantTeamProfile;
  },
};
