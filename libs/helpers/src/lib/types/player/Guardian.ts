import { z } from "zod";

import { CustomDate } from "../shared/Date";
import { Email } from "../shared/Email";
import { Phone } from "../shared/Phone";
import { StringOfLength } from "../shared/String";
import { zodToDomainOrThrow } from "../../misc";
import { Tagged, UUID } from "../shared";

export const embeddedGuardianParser = z.object({
  firstName: StringOfLength.parser(1, 100),
  lastName: StringOfLength.parser(1, 100),
  email: Email.parser,
  phone: Phone.parser,
  dob: CustomDate.dobParser(18, 90),
});

export type EmbeddedGuardian = z.infer<typeof embeddedGuardianParser>;

type GuardianId = Tagged<"GuardianId", UUID>;

export const guardianParser = embeddedGuardianParser.merge(
  z.object({
    id: UUID.parser<GuardianId>(),
  }),
);
export type Guardian = z.infer<typeof guardianParser>;

export const toEntityOrThrow = zodToDomainOrThrow(guardianParser);

export const createEntity = (
  dto: Omit<Guardian, "id">,
  id = UUID.generate<GuardianId>(),
): Guardian => {
  return {
    id,
    ...dto,
  };
};

export const updateEntity = (dto: Partial<Guardian>, entity: Guardian): Guardian => {
  return {
    ...entity,
    firstName: dto.firstName ?? entity.firstName,
    lastName: dto.lastName ?? entity.lastName,
    phone: dto.phone ?? entity.phone,
    email: dto.email ?? entity.email,
    dob: dto.dob ?? entity.dob,
  };
};
