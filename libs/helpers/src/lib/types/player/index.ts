import { z } from "zod";
import { omit, sample } from "lodash/fp";
import { faker } from "@faker-js/faker";

import { zodToDomain, zodToDomainOrThrow } from "../../misc";
import { OrganizationId } from "../organization";
import { PlayerTeamProfile } from "../player-team-profile";
import { PlayerTeamStatus } from "../player-team-profile/statuses";
import {
  Tagged,
  UUID,
  StringOfLength,
  CustomDate,
  Email,
  ParsingError,
  Phone,
  CustomEnum,
  EmailErrors,
  Pagination,
  PositiveInteger,
  DomainError,
  ErrorMessages,
  ValidDate,
} from "../shared";
import { TeamId } from "../team";
import { Assets } from "..";
import { Entity as ImageEntity } from "../assets/image";
import { basePlayerGenders, genderParser, PlayerGenders } from "../shared/Gender";
import { PlayingExperience } from "../shared/PlayingExperience";
import { addressParser } from "../shared/Address";
import { embeddedG<PERSON>ianParser, type Guardian } from "./Guardian";
import { documentsParser } from "../shared/Document";

export enum PlayerSorting {
  CreationDate = "creationDate",
  Names = "names",
  Age = "age",
}

export type PlayerId = Tagged<"PlayerId", UUID>;

const GUARDIAN_THRESHOLD = 18;
const OWN_EMAIL_THRESHOLD = 13;

const basicInfoParser = z.object({
  firstName: StringOfLength.parser(1, 100),
  lastName: StringOfLength.parser(1, 100),
  dob: CustomDate.dobParser(2, 60),
  phone: Phone.parser.optional(),
});

const requiresAGuardian = (dob: Date) => {
  const yearsAgo = CustomDate.getYearsAgo(dob);
  return yearsAgo < GUARDIAN_THRESHOLD;
};

const requiresAnEmail = (dob: Date) => {
  const yearsAgo = CustomDate.getYearsAgo(dob);
  return yearsAgo >= OWN_EMAIL_THRESHOLD;
};

const withGuardianValidation = <T extends z.ZodRawShape>(schema: z.ZodObject<T>) => {
  return schema.refine(
    (data) => {
      if (data.dob && requiresAGuardian(data.dob)) {
        return !!data.guardian;
      }
      return true;
    },
    {
      path: ["guardian"],
      message: "Required",
    },
  );
};

const baseParser = basicInfoParser.merge(
  z.object({
    email: Email.optionalParser,
    gender: genderParser,
    address: addressParser,
    guardian: embeddedGuardianParser.optional(),
    guardianId: UUID.parser<Guardian["id"]>().optional(),
    preferredPlayingPosition: StringOfLength.parser(1, 100).optional(),
    playingExperience: CustomEnum.parser(PlayingExperience),
    playingExperienceDescription: StringOfLength.parser(1, 300).optional(),
    medicalConditions: StringOfLength.parser(1, 200).optional(),
    acceptedTerms: z.boolean().default(false),
  }),
);

const fullParser = baseParser.merge(
  z.object({
    id: UUID.parser<PlayerId>(),
    documents: documentsParser.optional(),
  }),
);

export const parseId = z.object({ id: UUID.parser<PlayerId>() }).shape.id;

const createParser = withGuardianValidation(
  baseParser.merge(
    z.object({
      organizationId: UUID.parser<OrganizationId>(),
    }),
  ),
)
  .refine(
    (data: { dob: Date; email?: unknown }) => {
      if (data.dob && requiresAnEmail(data.dob)) {
        return Email.parser.safeParse(data.email).success;
      }
      return true;
    },
    {
      path: ["email"],
      message: EmailErrors.Required,
    },
  )
  .refine(
    (data: { dob: Date; email?: string; guardian?: { email?: string } }) => {
      if (
        requiresAnEmail(data.dob) &&
        requiresAGuardian(data.dob) &&
        data.email &&
        data.guardian?.email
      ) {
        return data.email !== data.guardian.email;
      }
      return true;
    },
    {
      path: ["email"],
      message: EmailErrors.SameGuardianPlayerEmail,
    },
  );

export type CreatePlayerDto = z.infer<typeof createParser>;

export type Player = z.infer<typeof fullParser>;

const updateDtoParser = baseParser
  .merge(z.object({ documents: documentsParser.optional() }))
  .refine(
    (data) => {
      if (data.dob && requiresAGuardian(data.dob)) {
        return !!data.guardian;
      }
      return true;
    },
    {
      path: ["guardian"],
      message: "Required",
    },
  );

export type UpdatePlayerDto = z.infer<typeof updateDtoParser>;

const populatedPlayerParser = fullParser.merge(
  z.object({
    profiles: z.array(PlayerTeamProfile.fullParser),
    photo: ImageEntity.parser.optional(),
    document_images: z.array(ImageEntity.parser).optional(),
  }),
);

export type PopulatedPlayer = z.infer<typeof populatedPlayerParser>;

const getPlayerByEmailDto = z.object({
  email: Email.parser,
});

const getPlayerByGuardianDto = z.object({
  guardianEmail: Email.parser,
  firstName: baseParser.shape.firstName,
  lastName: baseParser.shape.lastName,
});

export const playersDefaultPaging = { limit: 20, skip: 0 } as const;

const queryDtoParser = z.object({
  teamId: UUID.parser<TeamId>().optional(),
  playerStatus: CustomEnum.parser(PlayerTeamStatus).optional(),
  playerStatusNot: z.array(CustomEnum.parser(PlayerTeamStatus)).optional(),
  dobAfter: CustomDate.pastParser({ stripTime: true }).optional(),
  dobBefore: CustomDate.pastParser({ stripTime: true }).optional(),
  searchName: baseParser.shape.firstName.optional(),
  pagination: Pagination.pagingParamsParser.optional().default(playersDefaultPaging),
  sortBy: CustomEnum.parser(PlayerSorting).default(PlayerSorting.CreationDate),
});

export type PlayerQueryDto = z.infer<typeof queryDtoParser>;

export type GetPlayerByEmailDto = z.infer<typeof getPlayerByEmailDto>;

export type GetPlayerByGuardianDto = z.infer<typeof getPlayerByGuardianDto>;

const getPlayerDtoParser = z.union([getPlayerByEmailDto, getPlayerByGuardianDto]);

export type GetPlayerDto = z.infer<typeof getPlayerDtoParser>;

export type PlayersSearchParams = {
  orgId: OrganizationId;
  query: PlayerQueryDto;
};

const playersPaginatedResponseParser = Pagination.paginatedResponseParser(
  z.array(populatedPlayerParser),
);

export type PaginatedPopulatedPlayers = z.infer<typeof playersPaginatedResponseParser>;

export type ApplyToOrganizationDto = {
  playerId: PlayerId;
  organizationId: OrganizationId;
};

const leanParser = fullParser.pick({ id: true, firstName: true, lastName: true });

export type LeanPlayer = z.infer<typeof leanParser>;

export const Player = {
  /* used by BE controllers */
  toCreateDto: createParser,
  toCreateDtoOrThrow: zodToDomainOrThrow(createParser),
  toQueryDto: queryDtoParser,

  getPlayerDtoParser,
  fullParser,

  getDocumentsSubmitDate: (player: Player): ValidDate | undefined => {
    return player.documents?.submitted_on[player.documents?.submitted_on.length - 1];
  },

  toLean: (source: Player): LeanPlayer => {
    return {
      id: source.id,
      firstName: source.firstName,
      lastName: source.lastName,
    };
  },

  isPartOfOrganization: (player: PopulatedPlayer, orgId: OrganizationId) => {
    return !!player.profiles.find((elem) => elem.organizationId === orgId);
  },

  isOrganizationApplicant: (player: PopulatedPlayer) => {
    return (
      player.profiles.length === 1 &&
      (player.profiles[0].status === PlayerTeamStatus.ApplicantOrganization ||
        player.profiles[0].status === PlayerTeamStatus.RemovedTeam)
    );
  },

  hasActiveTeam: (player: PopulatedPlayer) => {
    return player.profiles.some((profile) => profile.status !== PlayerTeamStatus.RemovedTeam);
  },

  getFullName: (player: LeanPlayer) => {
    return `${player.firstName} ${player.lastName}`;
  },

  getGender: (player: Player) => {
    return player.gender.name === PlayerGenders.Other
      ? `Other: ${player.gender.description}`
      : player.gender.name;
  },

  getInitials: (player: Player) => {
    return player.firstName.charAt(0) + player.lastName.charAt(0);
  },

  getEmail: (player: Player) => {
    return (player.email || player.guardian?.email) as Email;
  },

  hasEmailIdentification: (source: GetPlayerDto): source is GetPlayerByEmailDto => {
    return "email" in source && !!source.email;
  },

  parseQueryDto: (source: unknown) => zodToDomain(queryDtoParser, source),

  parseGetPlayerDto: (source: unknown) => {
    const parsed = getPlayerDtoParser.safeParse(source);

    return parsed.success ? parsed.data : new ParsingError(parsed.error);
  },

  createNewPlayer: (dto: CreatePlayerDto, id = UUID.generate<PlayerId>()): Player => {
    return {
      ...dto,
      id,
    };
  },

  updatePlayer: (dto: Player, existingPlayer: Player): Player => {
    return {
      ...existingPlayer,
      ...omit("id", dto),
    };
  },

  /* used in the FE form */
  parseCreateDto: (source: unknown) => {
    const result = createParser.safeParse(source);

    if (result.success) {
      return result.data;
    }

    return new ParsingError(result.error);
  },

  toEntity: (source: unknown) => zodToDomain(fullParser, source),
  toEntityOrThrow: zodToDomainOrThrow(fullParser),

  toUpdateDto: (source: unknown) => zodToDomain(updateDtoParser, source),

  toPopulatedPlayer: (player: Player, profiles: PlayerTeamProfile[]): PopulatedPlayer => {
    return {
      ...player,
      profiles,
    };
  },

  toManyEntities: (source: unknown) => {
    const result = z.array(fullParser).safeParse(source);

    if (result.success) {
      return result.data;
    }

    return new ParsingError(result.error);
  },

  toPopulatedEntity: (source: unknown): PopulatedPlayer | ParsingError =>
    zodToDomain(populatedPlayerParser, source),

  toManyPopulatedEntities: (source: unknown) => zodToDomain(z.array(populatedPlayerParser), source),

  toPaginatedPopulatedPlayers: (source: unknown): ParsingError | PaginatedPopulatedPlayers =>
    zodToDomain(playersPaginatedResponseParser, source),

  requiresAGuardian,

  requiresAnEmail,

  parseId,
  leanParser,

  toPlayerId: (source: unknown) => zodToDomain(parseId, source),

  markDocumentsAsSubmitted: (player: Player, now = CustomDate.now()): Player => {
    if (player.documents?.submitted) {
      return player;
    }

    return {
      ...player,
      documents: {
        ...player.documents,
        submitted: true,
        submitted_on: [...(player.documents?.submitted_on || []), now],
      },
    };
  },

  markDocumentsAsUnsubmitted: (player: Player): Player => {
    return {
      ...player,
      documents: {
        ...player.documents,
        submitted_on: player.documents?.submitted_on || [],
        submitted: false,
      },
    };
  },

  addPhoto: (player: Player, photoId: Assets.Image.ImageId): Player | DomainError => {
    if (player.documents?.submitted) {
      return new DomainError(ErrorMessages.InvalidAction, {
        service: "PlayerEntity",
        message: "Cannot add photo to submitted player documents.",
      });
    }

    return {
      ...player,
      documents: {
        ...player.documents,
        submitted: !!player.documents?.submitted,
        submitted_on: player.documents?.submitted_on || [],
        photo_id: photoId,
      },
    };
  },

  removePhoto: (player: Player): Player | DomainError => {
    if (player.documents?.submitted) {
      return new DomainError(ErrorMessages.InvalidAction, {
        service: "PlayerEntity",
        message: "Cannot remove photo from submitted player documents.",
      });
    }

    return {
      ...player,
      documents: {
        ...player.documents,
        submitted: !!player.documents?.submitted,
        submitted_on: player.documents?.submitted_on || [],
        photo_id: undefined,
      },
    };
  },

  canAddImageDocument: (player: Player): boolean => {
    const existingDocuments = player.documents?.image_document_ids || [];

    return existingDocuments.length < 10 && !player.documents?.submitted;
  },

  addImageDocument: (player: Player, photoId: Assets.Image.ImageId): Player | DomainError => {
    const existingDocuments = player.documents?.image_document_ids || [];

    if (!Player.canAddImageDocument(player)) {
      return new DomainError(ErrorMessages.InvalidAction, {
        service: "PlayerEntity",
        message: "Cannot add more image documents.",
      });
    }

    if (existingDocuments.includes(photoId)) {
      return player;
    }

    return {
      ...player,
      documents: {
        ...player.documents,
        image_document_ids: [...existingDocuments, photoId],
        submitted: !!player.documents?.submitted,
        submitted_on: player.documents?.submitted_on || [],
      },
    };
  },

  removeImageDocument: (player: Player, photoId: Assets.Image.ImageId): Player | DomainError => {
    const existingDocuments = player.documents?.image_document_ids || [];

    if (player.documents?.submitted) {
      return new DomainError(ErrorMessages.InvalidAction, {
        service: "PlayerEntity",
        message: "Cannot remove image documents from submitted player documents.",
      });
    }

    return {
      ...player,
      documents: {
        ...player.documents,
        image_document_ids: existingDocuments.filter((elem) => elem !== photoId),
        submitted: !!player.documents?.submitted,
        submitted_on: player.documents?.submitted_on || [],
      },
    };
  },

  toDemoInstance: (id: PlayerId = UUID.generate<PlayerId>()) => {
    const gender = sample<"male" | "female">(["male", "female"]) || "male";

    const firstName = faker.name.firstName(gender);
    const lastName = faker.name.lastName(gender);
    const playerAge = sample([18, 19, 22, 25, 30]) || 18;

    return {
      id,
      firstName: firstName as StringOfLength<1, 100>,
      lastName: lastName as StringOfLength<1, 100>,
      dob: CustomDate.subYears(playerAge as PositiveInteger),
      phone: faker.phone.number(),
      email: `${firstName}.${lastName}@team-assist.co.uk` as Email,
      gender: {
        name: sample(Object.values(basePlayerGenders)) || PlayerGenders.M,
      },
      address: {
        address: faker.address.streetAddress(),
        postcode: faker.address.zipCode(),
      },
      playingExperience: PlayingExperience.Club,
      playingExperienceDescription: "played some quality footy" as StringOfLength<1, 300>,
      medicalConditions: "None" as StringOfLength<1, 200>,
      acceptedTerms: true,
    } as Player;
  },

  // Player under 18
  toDemoWithGuardianInstance: (id: PlayerId = UUID.generate<PlayerId>()) => {
    const gender = sample<"male" | "female">(["male", "female"]) || "male";
    const guardianGender = sample<"male" | "female">(["male", "female"]) || "male";

    const firstName = faker.name.firstName(gender);
    const lastName = faker.name.lastName(gender);

    const guardianFirstName = faker.name.firstName(guardianGender);
    const guardianLastName = faker.name.lastName(guardianGender);

    const playerAge = sample([12, 13, 14, 15, 16, 17]) || 12;

    return {
      id,
      firstName: firstName as StringOfLength<1, 100>,
      lastName: lastName as StringOfLength<1, 100>,

      // Player under 18
      dob: CustomDate.subYears(playerAge as PositiveInteger),
      guardian: {
        dob: CustomDate.subYears(35 as PositiveInteger),
        email: `${guardianFirstName}.${guardianLastName}@team-assist.co.uk` as Email,
        firstName: guardianFirstName,
        lastName: guardianLastName,
        phone: faker.phone.number(),
      },

      phone: faker.phone.number(),
      email: `${firstName}.${lastName}@team-assist.co.uk` as Email,
      gender: {
        name: sample(Object.values(basePlayerGenders)) || PlayerGenders.M,
      },
      address: {
        address: faker.address.streetAddress(),
        postcode: faker.address.zipCode(),
      },
      playingExperience: PlayingExperience.Club,
      playingExperienceDescription: "played some quality footy" as StringOfLength<1, 300>,
      medicalConditions: "None" as StringOfLength<1, 200>,
      acceptedTerms: true,
    } as Player;
  },
};
