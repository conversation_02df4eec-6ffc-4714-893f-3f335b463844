import { z } from "zod";

import { zodToDomain, zodToDomainOrThrow } from "../../misc";
import { OrganizationId } from "../organization";
import { Player, PlayerId } from "../player";
import {
  Tagged,
  UUID,
  StringOfLength,
  CustomDate,
  CustomEnum,
  PositiveInteger,
  ValidDate,
  DomainError,
  ErrorMessages,
  CustomNumber,
  ValidTime,
} from "../shared";

import { TeamId, TeamParser } from "../team/team";
import { Agenda, EventAddress, sharedEventFields } from "./shared";
import * as RecurringTeamEventEntity from "./recurringTeamEvent";
import { TrainingSessionReview } from "../training-session";

export type EventId = Tagged<"TeamEventId", UUID>;

export enum AttendanceStatus {
  Positive = "positive",
  Negative = "negative",
  Maybe = "maybe",
}

export enum TeamEventType {
  Single = "single",
  Recurring = "recurring",
}

const withIdParser = z.object({
  id: UUID.parser<EventId>(),
});

const attendanceParser = z.object({
  status: CustomEnum.parser(AttendanceStatus),
  player: Player.parseId,
  createdAt: CustomDate.validParser(),
  updatedAt: CustomDate.validParser().optional(),
});

export type Attendance = z.infer<typeof attendanceParser>;

const baseParser = z.object({
  organizationId: UUID.parser<OrganizationId>(),
  teamId: UUID.parser<TeamId>(),

  notes: StringOfLength.parser(2, 2000).optional(),
  isCancelled: z.boolean().optional(),

  startDateTime: CustomDate.validParser(),
  endDateTime: CustomDate.validParser(),

  meetupMinutesBefore: CustomNumber.parsePositiveInteger.optional(),

  respondents: z.array(attendanceParser).optional(),
  remindersSent: z
    .array(
      z.object({
        playerId: Player.parseId,
        sentAt: CustomDate.validParser(),
      }),
    )
    .optional(),
});

const singleParser = baseParser
  .merge(sharedEventFields)
  .merge(
    z.object({
      type: z.literal(TeamEventType.Single),
    }),
  )
  .merge(withIdParser);

const recurringParser = z
  .object({
    type: z.literal(TeamEventType.Recurring),
    recurringEventId: UUID.parser<RecurringTeamEventEntity.EventId>(),
  })
  .merge(baseParser)
  .merge(withIdParser);

const createSingleDtoParser = baseParser.merge(sharedEventFields);

export type CreateSingleDto = z.infer<typeof createSingleDtoParser>;

const upsertSingleDtoParser = createSingleDtoParser.merge(
  z.object({
    id: withIdParser.shape.id.optional(),
  }),
);
export type UpsertSingleDto = z.infer<typeof upsertSingleDtoParser>;

const createRecurringDtoParser = baseParser;

export type CreateRecurringDto = z.infer<typeof createRecurringDtoParser>;

const parser = z.discriminatedUnion("type", [singleParser, recurringParser]);

export type SingleTeamEvent = z.infer<typeof singleParser>;
export type RecurringTeamEvent = z.infer<typeof recurringParser>;
export type TeamEvent = z.infer<typeof parser>;

const queryDtoParser = z.object({
  startDate: CustomDate.validParser({ stripTime: true }).optional(),
  endDate: CustomDate.validParser({ stripTime: true }).optional(),
  agenda: CustomEnum.parser(Agenda).optional(),
});

export type QueryDto = z.infer<typeof queryDtoParser>;

export const extendedParser = z.discriminatedUnion("type", [
  singleParser.merge(
    z.object({
      recurringEvent: z.literal(undefined),
      trainingSessionReview: z
        .object({ id: UUID.parser<TrainingSessionReview.ReviewId>() })
        .optional(),
    }),
  ),
  recurringParser.merge(
    z.object({
      recurringEvent: RecurringTeamEventEntity.Entity.parser,
      trainingSessionReview: z
        .object({ id: UUID.parser<TrainingSessionReview.ReviewId>() })
        .optional(),
    }),
  ),
]);

/* provide only some fields to players, the others are for coaches only */
const playerEventParser = z.discriminatedUnion("type", [
  singleParser
    .pick({
      type: true,
      id: true,
      name: true,
      location: true,
      agenda: true,
      hosts: true,
      respondents: true,
      description: true,
      startDateTime: true,
      endDateTime: true,
      isCancelled: true,
      meetupMinutesBefore: true,
      notes: true,
    })
    .merge(
      z.object({
        recurringEvent: z.literal(undefined),
        team: TeamParser.pick({ name: true, id: true }),
      }),
    ),
  recurringParser
    .pick({
      type: true,
      id: true,
      startDateTime: true,
      endDateTime: true,
      isCancelled: true,
      respondents: true,
      meetupMinutesBefore: true,
      notes: true,
    })
    .merge(
      z.object({
        recurringEvent: RecurringTeamEventEntity.Entity.parser.pick({
          id: true,
          name: true,
          description: true,
          location: true,
          agenda: true,
          hosts: true,
        }),
        team: TeamParser.pick({ name: true, id: true }),
      }),
    ),
]);

export type PlayerTeamEvent = z.infer<typeof playerEventParser>;

export type ExtendedTeamEvent = z.infer<typeof extendedParser>;

const attendanceDtoParser = attendanceParser.pick({ status: true });

export type AttendanceDto = z.infer<typeof attendanceDtoParser>;

/* simple typed wrapper used in APIs returning counts */
const unansweredEventsCountParser = z.object({
  count: CustomNumber.parsePositiveInteger,
});

export type UnansweredEventsCount = z.infer<typeof unansweredEventsCountParser>;

export const Entity = {
  toEntity: (source: unknown) => zodToDomain(parser, source),

  toSingleEventEntity: (source: unknown) => zodToDomain(singleParser, source),

  toEntities: (source: unknown) => zodToDomain(z.array(parser), source),

  toExtendedEntities: (source: unknown) => zodToDomain(z.array(extendedParser), source),

  toExtendedEntity: (source: unknown) => zodToDomain(extendedParser, source),

  toPlayerEventEntities: (source: unknown) => zodToDomain(z.array(playerEventParser), source),

  toPlayerEventEntity: (source: unknown) => zodToDomain(playerEventParser, source),

  toCreateSingleDto: (source: unknown) => zodToDomain(createSingleDtoParser, source),

  toUpsertSingleDto: (source: unknown) => zodToDomain(upsertSingleDtoParser, source),

  createOrUpdateAttendance: (
    event: TeamEvent,
    playerId: PlayerId,
    dto: AttendanceDto,
    now = CustomDate.now(),
  ): Attendance | DomainError => {
    if (CustomDate.isAfter(now, event.startDateTime)) {
      return new DomainError(ErrorMessages.InvalidAction, {
        message: "Trying to respond to an expired event.",
      });
    }

    const existingRecordForPlayer = event.respondents?.find((elem) => elem.player === playerId);

    return existingRecordForPlayer
      ? { ...existingRecordForPlayer, status: dto.status, updatedAt: now }
      : { ...dto, player: playerId, createdAt: now };
  },

  updateEventAttendance: (event: TeamEvent, attendance: Attendance): TeamEvent => {
    const existingResponses = event.respondents || [];
    const existingAttendance = existingResponses.find((elem) => elem.player === attendance.player);

    return {
      ...event,
      respondents: existingAttendance
        ? existingResponses.map((elem) =>
            elem.player === existingAttendance.player ? attendance : elem,
          )
        : [...existingResponses, attendance],
    };
  },

  createSingle: (dto: CreateSingleDto, id = UUID.generate<EventId>()): SingleTeamEvent => {
    return {
      id,
      type: TeamEventType.Single,
      ...dto,
      respondents: [],
    };
  },

  createRecurring: (
    recurringEvent: RecurringTeamEventEntity.RecurringTeamEvent,
    date: ValidDate,
    id = UUID.generate<EventId>(),
  ): RecurringTeamEvent => {
    return {
      id,
      type: TeamEventType.Recurring,
      organizationId: recurringEvent.organizationId,
      teamId: recurringEvent.teamId,
      recurringEventId: recurringEvent.id,

      startDateTime: CustomDate.mergeDateTime(date, recurringEvent.schedule.startTime),
      endDateTime: CustomDate.mergeDateTime(date, recurringEvent.schedule.endTime),

      respondents: [],
    };
  },

  createSingleDtoParser,
  parser,
  extendedParser,
  parseId: withIdParser.shape.id,
  attendanceDtoParser,

  unansweredEventsCountParser,

  toUnansweredEventsCount: (source: unknown) =>
    zodToDomainOrThrow(unansweredEventsCountParser, source),

  queryDtoParser,

  playerIsInvitedToEvent: (event: ExtendedTeamEvent, playerId: PlayerId) => {
    if (event.type === TeamEventType.Single) {
      return event.invitations.includes(playerId);
    }

    return event.recurringEvent.invitations.includes(playerId);
  },

  toEventId: (source: unknown) => zodToDomain(UUID.parser<EventId>(), source),

  constructFutureRange: (days: PositiveInteger, anchor = CustomDate.now()): QueryDto => {
    return {
      startDate: anchor,
      endDate: CustomDate.addDays(days, () => anchor),
    };
  },

  constructPastRange: (days: PositiveInteger, anchor = CustomDate.now()): QueryDto => {
    return {
      startDate: CustomDate.subDays(days, () => anchor),
      endDate: anchor,
    };
  },

  toQueryDto: (source: unknown) => zodToDomain(queryDtoParser, source),

  getName: (event: ExtendedTeamEvent | PlayerTeamEvent) => {
    return event.type === TeamEventType.Single ? event.name : event.recurringEvent.name;
  },

  isPast: (event: TeamEvent) => {
    return CustomDate.isPastDate(event.endDateTime);
  },

  getLocation: (event: PlayerTeamEvent): EventAddress => {
    return event.type === TeamEventType.Single ? event.location : event.recurringEvent.location;
  },

  getLocationLink: (event: PlayerTeamEvent): string => {
    const location =
      event.type === TeamEventType.Single ? event.location : event.recurringEvent.location;
    return `https://www.google.com/maps/search/?api=1&query=${encodeURIComponent(
      location.address.split(" ").join("+"),
    )}, ${encodeURIComponent(location.postcode)}`;
  },

  getAttendance: (event: PlayerTeamEvent, playerId: PlayerId): Attendance | undefined => {
    return event.respondents?.find((elem) => elem.player === playerId);
  },

  getAgenda: (event: PlayerTeamEvent) => {
    if (event.type === TeamEventType.Single) {
      return event.agenda;
    }

    return event.recurringEvent.agenda;
  },

  getMeetupTime: (event: PlayerTeamEvent): ValidTime | undefined => {
    if (event.meetupMinutesBefore) {
      return CustomDate.timeSubMinutes(event.meetupMinutesBefore, () => event.startDateTime);
    }

    return undefined;
  },

  getDescription: (event: PlayerTeamEvent) => {
    return event.type === TeamEventType.Single
      ? event.description
      : event.recurringEvent.description;
  },
};

export { Agenda };
