- you must read and follow .ai-assets/docs/agent-instructions.md before starting any task.
- you MUST always read `.ai-assets/overview.md` to get a comprehensive overview of the project, architecture, and monorepo structure.

- If doing work in a remote agent environment - check the .nvmrc file for the exact node version and use it, run `npm install` and run diagnostics like lint, type-check, build, test via `npx nx`

- If the user asks you to open a pull request for them - make sure within the description to note that this was opened by gemini-cli as a request from {the current user}. You're the one drafting the description. The base branch is always "main" unless explicitly stated otherwise.
