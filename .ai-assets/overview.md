# Project Overview

This file provides guidance to WA<PERSON> (warp.dev) when working with code in this repository.

## Project Overview

Coach Center is a sports management platform built as an NX monorepo with multiple applications for coaches and players. It includes team management, training sessions, match analysis, payment processing, and player portals.

## Architecture Overview

### Monorepo Structure

- **NX Workspace**: Uses NX for build orchestration and dependency management
- **Apps**: Multiple applications serving different user roles
- **Libs**: Shared libraries for common functionality and types
- **Node.js**: Version 22.1.0 (managed via `.nvmrc`)

### Applications

- `api`: NestJS REST API backend serving all frontend applications
- `client`: React SPA for coaches (team management, match analysis, training plans)
- `player-portal`: React SPA for players (events, documents, payments)
- `marketing`: Next.js landing page for product marketing
- `match-mode`: React SPA for live match tracking

### Shared Libraries

- `libs/helpers`: Common types, utilities, and test helpers
- `libs/ui`: Shared UI components, themes, and design system

### Technology Stack

- **Backend**: NestJS, MongoDB with replica sets, JWT auth, Sentry monitoring
- **Frontend**: React 18, Material-UI, React Query, React Router
- **Build**: NX, Webpack, SWC compiler
- **Testing**: Jest, Cypress for E2E
- **Infrastructure**: Docker, Azure Blob Storage, SendGrid, Stripe

## Development Commands

### Prerequisites Setup

```bash
# Install correct Node.js version
nvm install && nvm use

# Install dependencies
npm install

# Start MongoDB (required for API)
docker-compose up -d

# Initialize MongoDB replica set (first time only)
docker exec mongodb mongosh --eval 'rs.initiate({_id: "rs0", members: [{_id: 0, host: "mongodb:27017"}]})'

# Copy API environment file
cp apps/api/.env.example apps/api/.env
```

### Core Development Commands

```bash
# Start all applications in parallel
npm start

# Start specific applications
npm run start:api          # API only (port 3333)
nx serve client           # Coach app (port 4001)
nx serve player-portal    # Player app (port 4002)
nx serve marketing        # Marketing site

# Build all projects
npm run build

# Run tests
npm test                  # All tests in parallel
nx test api              # API tests only
nx test client           # Client tests only
nx test:watch api        # Watch mode for specific app

# Linting and formatting
npm run lint             # ESLint all projects
npm run format:check     # Prettier check
npm run format:write     # Apply Prettier formatting
npm run type-check       # TypeScript checking

# Database migrations
npm run create-migration <name>    # Create new migration
npm run perform-migrations         # Run pending migrations
npm run status-migrations         # Check migration status
```

### Environment-Specific Builds

```bash
# Production builds
npm run build-client              # Client production build
npm run build-player-portal       # Player portal production build
npm run build-marketing           # Marketing site build

# Staging builds
npm run build-staging-client
npm run build-staging-player-portal

# Demo builds
npm run build-demo-client
npm run build-demo-player-portal
```

### Docker & Deployment

```bash
# Build API Docker image
npm run api:docker

# Bundle analysis
npm run analyze:client    # Webpack bundle analyzer
```

## Key Architecture Patterns

### API Architecture (NestJS)

- **Modular Structure**: Each domain (auth, players, teams, training, matches) is a separate module
- **Guards**: API key authentication, JWT auth, profile guards, permission-based access
- **Repositories**: MongoDB repositories with soft delete patterns
- **Event System**: NestJS EventEmitter for domain events
- **Error Handling**: Sentry integration with custom error filters

### Database Patterns

- **MongoDB**: Primary database with replica set configuration for transactions
- **Soft Deletes**: Most entities support soft deletion rather than hard deletes
- **Migrations**: JavaScript-based migrations using migrate-mongo
- **Indexes**: Strategic indexing for performance (check migration files)

### Frontend Architecture

- **Shared State**: React Query for server state, React context for local state
- **Routing**: React Router with route-based code splitting
- **UI Library**: Material-UI with custom theme and components in `libs/ui`
- **Forms**: React Hook Form with validation
- **Internationalization**: i18next for multi-language support

### Shared Libraries Usage

- `@coach-center/helpers`: Import types and utilities across apps
- `@coach-center/ui`: Import shared components, theme, and design tokens
- Cross-app imports follow NX dependency rules defined in project.json files

### Environment Configuration

- **Development**: Uses environment.ts files with hot reload
- **Production**: Uses environment.prod.ts with optimizations
- **Staging/Demo**: Separate environment files for different deployments
- **API Keys**: Stored in .env files (never committed)

### Authentication & Authorization

- **JWT Tokens**: Used across all applications
- **Role-Based**: Permissions system with guards on API routes
- **Multi-tenant**: Organization-based data separation
- **Player Auth**: Separate authentication flow for player portal

### File Upload & Storage

- **Azure Blob Storage**: Primary file storage for production
- **Local Storage**: Development mode file storage
- **Image Processing**: Automatic image optimization and resizing

### Payment Processing

- **Stripe**: Primary payment processor
- **GoCardless**: UK Direct Debit payments
- **Subscription Management**: Handles recurring payments and billing

## Important Configuration Notes

### MongoDB Setup

MongoDB must run as a replica set even in development to support transactions. Use the provided docker-compose.yml and initialization script.

### Environment Variables

The API requires a .env file with database connection, API keys, and service configurations. Never commit real credentials.

### Port Configuration

- API: 3333 (with /api prefix)
- Client (Coach App): 4001
- Player Portal: 4002
- Marketing: Default Next.js port

### CORS Configuration

Development CORS is configured for localhost:4001 and localhost:4002. Production uses environment-specific origins.

### Build Optimization

- Production builds use SWC for faster compilation
- Bundle size limits are enforced (500KB warning, 2MB error)
- Source maps disabled in production for security

### Testing Strategy

- Unit tests: Jest with SWC transformation
- E2E tests: Cypress for critical user flows
- API tests: Integration tests with in-memory MongoDB
- Frontend tests: React Testing Library patterns

This architecture supports a multi-tenant sports management platform with separate interfaces for coaches and players, comprehensive payment processing, and detailed performance analytics.
