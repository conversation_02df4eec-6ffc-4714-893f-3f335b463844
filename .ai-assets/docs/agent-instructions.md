- you must always read .ai-assets/docs/style-guide.md before starting any task.
- you should proactively ask more clarifying questions to improve your context

- if you're doing front end work you must read and follow .ai-assets/docs/front-end-guide.md.
- if you're doing back end work you must read and follow .ai-assets/docs/back-end-guide.md.
- if asked general or architectural questions you must read and follow .ai-assets/docs/exploring.md.
- For a comprehensive overview of the project, monorepo, and architecture, you must read `.ai-assets/overview.md`.
- if the user asks you to use "feature planning" as part of your workflow you must read and follow `.ai-assets/docs/feature-planning.md`.
- do not attempt writing tests unless the user asks you explicitly to do so. If told to write tests you must read .ai-assets/docs/testing.md.
- when trying to fix issues or failing tests - try a maximum of 3 times and then ask for help and provide any useful clue you can think of.
- a task is not considered done unless the diagnostics pass - you must read .ai-assets/docs/diagnostics.md and follow it strictly.

- You can find general information about the product, tech, coding style guide and architecture inside `.ai-assets/docs` folder. .ai-assets/overview.md contains a comprehensive overview of the repository.
