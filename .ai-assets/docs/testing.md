### Backend, nest.js

- Use `apps/api/src/app/auth/auth.spec.ts` as an example
- Always use `ErrorMessages` constants from `@mio/helpers` instead of hardcoded error message strings in tests. For example:

  ```typescript
  // Instead of:
  expect(response.body.message).toBe("PermissionDenied");

  // Do this:
  import { ErrorMessages } from "@mio/helpers";
  // ...
  expect(response.body.message).toBe(ErrorMessages.PermissionDenied);
  ```

  This ensures consistency and makes it easier to update error messages in the future.

- use the `apiUrls` variable when doing requests to the API, do not forget the `leadSlash` param:

```typescript
import request from "supertest";
import { apiUrls } from "@mio/helpers";

await request(app.getHttpServer()).get(
  buildUrlWithParams(
    apiUrls.playerEvents,
    { playerId: player.id },
    {
      leadSlash: true,
    },
  ),
);
```

- try to write more end-to-end style tests rather than ones with shallow mocking as it provides more realism. In the recommended example file you can see how we use in-memory mongodb to facilitate that. - we call an endpoint, let the logic go all the way down ot the database and then assert the DB entries themselves.

- if the previous rule is hard to implement for a given situation we want to test - call it out and ask the human for help and further guidance.

- check ./back-end-guide.md for more details about the backend.

### Front end

We use the notion of an `SDKProvider` wrapping all our react components. It passes down functions that the rest of the code can use to fetch or mutate server data. That's the right point for you to apply stubbing or mocking when testing any logic that includes http calls.
