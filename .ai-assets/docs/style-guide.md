### General

- you must use strict typescript types, if forced to use `any` or type cast - you must leave a comment explaining why this was needed
- you must use our nominal type helpers imported from `@mio/helpers`. Example:

```typescript
// goood
const parser = z.object({
  name: StringOfLength.parser(1, 30),
});

// bad
const parser = z.object({
  name: z.string(),
});
```

- you must use strict parsing at the application boundaries for inputs/outputs:

```typescript

- use the following import style: `import { type SomeType, SomeRuntimeFunc } from 'some-module'` so that we have more effective tree-shaking

//// FRONT END

// good
const data = await fetch(...).then(res => zodParser.parse(res.json()))

// bad
const data: SomeType = await fetch(...)

//// BACK END

// good
const item = await repo.findOne(...).then(res => zodParser.parse(res))
// bad
const item: SomeType = await repo.findOne(...)

```

- for internal application logic you must rely on our strict types instead of runtime parsing:

```typescript

// good
sendMessage(message: StringOfLength<1, 50>) {}
applyChanges(items: NonEmptyArray<Item>) {}

// bad - we should receive a strict param type instead
sendMessage(message: string) {
  const parsed = StringOfLength.parse(message);
}
applyChanges(items: Item[]) {
  if (items.length === 0) {
    // early return
  }
}
```

- you must use `type` over `interface`
- you must use `const someFunc = ()` over `function someFunc()`
- if a `zod` parser exists - the type must be derived from it. Example:

```typescript
// good
const itemParser = z.object(...)
export type ItemParser = z.infer<typeof itemParser>

// bad - no need to manually construct the type
const itemParser = z.object(...)
export type ItemParser = { a, b, c}

```

- functions with long parameter lists must use a single object instead. Example:

```typescript
// bad
const doSomething(a, b, c, d) => {...}

// good
const doSomething(params: {a, b, c, d} ) => {...}
```

- for referencing api paths either in FE or BE code you must use the centralized `api-urls.ts` variables, if needed - with `buildUrlWithParams` and `UrlParams`. Example:

```typescript
// BE controller:
@Post(apiUrls.login)
async login(...) { ... }

// FE fetcher:
axios.post(apiUrls.login, payload)
axios.get(buildUrlWithParams(apiUrls.player, { UrlParams.PlayerId: player.id}))

```

- you must leave only meaningful comments for special cases explaining more `why` rather than `what`. Example:

```typescript
// GOOD, useful comment
// we're handling this thing here, because that and that
if (some-not-so-standard-condition) {...}

// BAD useless comment
// gather the items from the DB
fetchItems(...)
```

### Error handling

- you must use our custom error classes - `UnexpectedError`, `DomainError` and `ParsingError` for input/output boundaries strictly. For internal logic it's allowed to create additional custom error classes which will be converted to the 3 main ones when they reach a boundary, e.g. returning from a BE controller.
- you must add additional context to errors. Example:

```typescript
new DomainError(..., { service, method, message, ..whatever else is useful } )
new DomainError().addContext({ service, method, message, ..whatever else is useful })
.. same for UnexpectedError, ParsingError
```

### Namespaced Imports for Primary Entities

When importing from a module that defines a primary entity, prefer importing the primary entity's namespace directly. Use this namespace to access related subtypes, enums, and parsers (e.g., `Team.TeamId`, `Player.PlayerGenders`). This practice helps avoid type conflicts and ensures consistent type resolution.

### Examples

#### Team Entity

```typescript
// Import the Team namespace
import { Team } from '@helpers/types/team';

// Use the namespace to access related types and enums
const teamId: Team.TeamId = ...;
const gender: Team.TeamGenders = Team.TeamGenders.Male;
```

#### Player Entity

```typescript
// Import the Player namespace
import { Player } from '@helpers/types/player';

// Use the namespace to access related types and enums
const playerId: Player.PlayerId = ...;
const gender: Player.PlayerGenders = Player.PlayerGenders.Male;
```

#### Organization Entity

```typescript
// Import the Organization namespace
import { Organization } from '@helpers/types/organization';

// Use the namespace to access related types and enums
const orgId: Organization.OrganizationId = ...;
const slug: Organization.OrganizationSlug = ...;
```
