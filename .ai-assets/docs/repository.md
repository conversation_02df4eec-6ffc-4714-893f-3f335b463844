# NX Monorepo Configuration

## Current State

- Nx is configured per project through project.json files
- Centralized configuration in nx.json with generators for React and Next.js applications

## Key Projects

### API

- **Type**: NestJS application
- **Location**: apps/api/
- **Purpose**: Main backend API serving all frontend applications
- **Key Features**:
  - Built with Webpack
  - Node.js target
  - SWC compiler
  - Docker support for production builds
  - Environment-specific configurations (prod, dev)
- **Key Files**:
  - src/main.ts - Entry point
  - src/environments/ - Environment configurations
  - webpack.config.js - Webpack configuration
  - Dockerfile - Docker configuration

### Marketing

- **Type**: Next.js application
- **Location**: apps/marketing/
- **Purpose**: Marketing landing page for the product
- **Key Features**:
  - Built with Next.js
  - Environment-specific configurations (prod, dev)
  - Export capability for static site generation
- **Key Files**:
  - environments/ - Environment configurations
  - next.config.js - Next.js configuration

### Client

- **Type**: React admin application
- **Location**: apps/client/
- **Purpose**: Coach-facing application for team and player management
- **Key Features**:
  - Built with Webpack
  - Multiple environment configurations (prod, staging, demo)
  - Hot module replacement for development
  - Budget enforcement for bundle sizes
- **Key Files**:
  - src/main.tsx - Entry point
  - src/environments/ - Environment configurations
  - src/assets/ - Static assets

### Player Portal

- **Type**: React application
- **Location**: apps/player-portal/
- **Purpose**: Player-facing application
- **Key Features**:
  - Built with Webpack
  - Multiple environment configurations (prod, staging)
  - Hot module replacement for development
  - Budget enforcement for bundle sizes
- **Key Files**:
  - src/main.tsx - Entry point
  - src/environments/ - Environment configurations
  - src/assets/ - Static assets

### Match Mode

- **Type**: React PWA
- **Location**: apps/match-mode/
- **Purpose**: Progressive Web App for match-related functionality
- **Key Features**:
  - Built with Vite
  - Environment-specific configurations (prod, dev)
  - Preview server capability
  - Static file serving
- **Key Files**:
  - src/environments/ - Environment configurations
  - vite.config.ts - Vite configuration

## Shared Libraries

### Helpers

- **Type**: Cross-platform utilities library
- **Location**: libs/helpers/
- **Purpose**: Shared utility functions and types across projects
- **Key Features**:
  - TypeScript-based
  - Comprehensive type definitions
  - Test coverage with Jest
- **Key Files**:
  - src/lib/ - Library source code
  - src/lib/types/ - Type definitions
  - jest.config.ts - Test configuration

### UI

- **Type**: React/MUI component library
- **Location**: libs/ui/
- **Purpose**: Shared UI components based on Material UI
- **Key Features**:
  - Emotion for CSS-in-JS
  - Storybook support for component development
  - Test coverage with Jest
- **Key Files**:
  - src/lib/compositions/ - Component compositions
  - src/assets/ - Shared assets (fonts, icons)
  - jest.config.ts - Test configuration
  - storybook configuration

## Development Setup

1. Node.js version management with nvm (.nvmrc file)
2. Docker-compose for local MongoDB instance
3. Nx Console VSCode extension recommended
4. Centralized package.json and node_modules - the individual apps and libs don't have their own
5. Local .env file required for API project

## Essential Data Seeding

1. Create Organization via Coach portal
2. Create invite with organization ID
3. Register coach user with invite ID
4. Login and start creating teams

## Build and Test Commands

- `nx build [project]` - Build project
- `nx serve [project]` - Serve project
- `nx test [project]` - Test project
- `nx lint [project]` - Lint project
- `nx run-many --target=[command] --all --parallel` - Run command for all projects
