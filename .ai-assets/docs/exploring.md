- you must read .ai-assets/overview.md to get a comprehensive overview of the project, architecture, and monorepo structure.
- you must read .ai-assets/docs/product.md to get an overview of the product and its features.
- you must read .ai-assets/docs/style-guide.md to get an overview of the coding style guide.
- you must read .ai-assets/docs/front-end-guide.md to get an overview of the front end guide.
- you must read .ai-assets/docs/back-end-guide.md to get an overview of the back end guide.

- your answer should follow the following structure:

```
### Overview of the goal

### What I found

### What I think we should do

### What's the biggest risk

### What's the biggest challenge

### What's good for us given the current state of the project

### Next steps

### Additional notes
```

- you should proactively ask more clarifying questions to improve your understanding of the goal and problem
