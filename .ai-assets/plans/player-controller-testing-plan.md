# Player Controller Integration Testing Plan

## Overview

This plan outlines the comprehensive integration testing strategy for the `PlayerController` class using real services and in-memory MongoDB. The tests will follow the same pattern as the existing `auth.spec.ts` e2e tests, testing the full request-response cycle with real business logic.

## Test Structure Analysis

Based on the existing auth.spec.ts pattern, the following structure should be used:

### 1. Test File Organization

- **File**: `apps/api/src/app/player/player.controller.spec.ts`
- **Pattern**: Integration tests using real NestJS application with in-memory MongoDB
- **Framework**: Jest with NestJS testing utilities and supertest for HTTP requests

### 2. Test Bed Configuration

```typescript
import { Test, TestingModule } from "@nestjs/testing";
import { INestApplication } from "@nestjs/common";
import * as request from "supertest";

import { AppModule } from "../app.module";
import { apiUrls, ErrorMessages, ErrorTypes } from "@mio/helpers";
import { AppTestService } from "../test/test.service";
import { DatabaseModule } from "../database/database.module";
import { createInMemoryMongoClient } from "../../test/memory-mongo";
import { DATABASE_CLIENT, DATABASE_CONNECTION, DBClient } from "../database";
import { playerFactory } from "../../test/factories/player";
import { organizationFactory } from "../../test/factories/organization";
import { teamFactory } from "../../test/factories/team";
import { coachUserFactory } from "../../test/factories/coach-user";
import { profileFactory } from "../../test/factories/profile";
```

### 3. Real Dependencies with In-Memory Database

#### Real Components Used

- **PlayerService**: Real service with all business logic
- **PlayerRepository**: Real repository with MongoDB operations
- **All other services**: Real implementations (TeamService, AssetsService, etc.)
- **Guards and Pipes**: Real validation and authorization logic
- **Event System**: Real EventEmitter2 for testing event emissions

#### Only Mocked Component

- **MongoDB**: Replaced with mongodb-memory-server for isolated testing

## Integration Test Setup

### 1. Application Setup Pattern (Following auth.spec.ts)

```typescript
describe("player controller integration", () => {
  let app: INestApplication;
  let testService: AppTestService;
  let mongoClient: DBClient;

  beforeAll(async () => {
    const fakeMongo = await createInMemoryMongoClient();
    mongoClient = fakeMongo.db;

    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [AppModule],
      providers: [],
    })
      .overrideModule(DatabaseModule)
      .useModule({
        module: class TestDatabaseModule {},
        imports: [],
        providers: [
          {
            provide: DATABASE_CONNECTION,
            useFactory: async () => fakeMongo.db,
          },
          {
            provide: DATABASE_CLIENT,
            useFactory: async () => fakeMongo.mongoClient,
          },
        ],
        exports: [DATABASE_CONNECTION, DATABASE_CLIENT],
      })
      .compile();

    app = moduleFixture.createNestApplication();
    await app.init();

    testService = new AppTestService(mongoClient);
  });

  afterEach(async () => {
    // Clear all collections between test cases
    const collections = await mongoClient.collections();
    for (const collection of collections) {
      await collection.deleteMany({});
    }
  });

  afterAll(async () => {
    await app.close();
  });
});
```

### 2. Test Data Setup Using AppTestService

The existing `AppTestService` provides methods for setting up test data:

- `getOrgWithCoach()` - Creates organization with coach user and profile
- `createOrgWithUserInvite()` - Creates organization with invite
- `createTeam()` - Creates teams for organizations
- `createTeamWithPlayers()` - Creates teams with player applicants
- `joinOrganization()` - Creates player-organization relationships
- `addOwnerPermission()` - Sets up permissions for testing
- `addCustomPermission()` - Sets up custom role permissions

### 3. Entity Factories for Test Data

All existing factories will be used for creating test entities:

- `playerFactory` - For Player entities
- `organizationFactory` - For Organization entities
- `teamFactory` - For Team entities
- `profileFactory` - For Profile entities
- `coachUserFactory` - For CoachUser entities
- `permissionFactory` - For Permission entities
- `roleFactory` - For Role entities

## Controller Methods to Test with HTTP Integration

### 1. Document Management Methods

#### `GET /players/:playerId/documents/images` - getPlayerDocumentImages

- **Authentication**: Requires PlayerUser JWT token
- **Authorization**: PlayerUserGuard, PlayerManageGuard (player or guardian)
- **Success Cases**:
  - Returns 200 with array of ImageAsset objects
  - Player can access their own documents
  - Guardian can access their player's documents
- **Error Cases**:
  - 401 Unauthorized when no token provided
  - 403 Forbidden when user cannot manage this player
  - 404 Not Found when player doesn't exist
  - 500 Internal Server Error for unexpected errors

#### `PATCH /players/:playerId/documents/submit` - submitPlayerDocuments

- **Authentication**: Requires PlayerUser JWT token
- **Authorization**: PlayerUserGuard, PlayerManageGuard
- **Success Cases**:
  - Returns 200 when documents successfully submitted
  - Updates player document status in database
- **Error Cases**:
  - 400 Bad Request when documents incomplete
  - 401 Unauthorized when no token provided
  - 403 Forbidden when user cannot manage this player
  - 500 Internal Server Error for unexpected errors

#### `PATCH /players/:playerId/documents/photo/:imageId` - addPlayerPhoto

- **Authentication**: Requires PlayerUser JWT token
- **Authorization**: PlayerUserGuard, PlayerManageGuard, PlayerOwnsImageGuard
- **Success Cases**:
  - Returns 200 with updated Player entity
  - Photo is properly associated with player
- **Error Cases**:
  - 400 Bad Request when image doesn't belong to user
  - 401 Unauthorized when no token provided
  - 403 Forbidden when user cannot manage this player
  - 500 Internal Server Error for unexpected errors

#### `DELETE /players/:playerId/documents/photo` - removePlayerPhoto

- **Authentication**: Requires PlayerUser JWT token
- **Authorization**: PlayerUserGuard, PlayerManageGuard
- **Success Cases**:
  - Returns 200 with updated Player entity
  - Photo reference removed from player
- **Error Cases**:
  - 401 Unauthorized when no token provided
  - 403 Forbidden when user cannot manage this player
  - 404 Not Found when player has no photo
  - 500 Internal Server Error for unexpected errors

#### `PATCH /players/:playerId/documents/image/:imageId` - addPlayerImageDocument

- **Authentication**: Requires PlayerUser JWT token
- **Authorization**: PlayerUserGuard, PlayerManageGuard, PlayerOwnsImageGuard
- **Success Cases**:
  - Returns 200 with updated Player entity
  - Document image added to player's documents
- **Error Cases**:
  - 400 Bad Request when image doesn't belong to user
  - 401 Unauthorized when no token provided
  - 403 Forbidden when user cannot manage this player
  - 500 Internal Server Error for unexpected errors

#### `DELETE /players/:playerId/documents/image/:imageId` - removePlayerImageDocument

- **Authentication**: Requires PlayerUser JWT token
- **Authorization**: PlayerUserGuard, PlayerManageGuard
- **Success Cases**:
  - Returns 200 with updated Player entity
  - Document image removed from player's documents
- **Error Cases**:
  - 401 Unauthorized when no token provided
  - 403 Forbidden when user cannot manage this player
  - 404 Not Found when image not found in player documents
  - 500 Internal Server Error for unexpected errors

### 2. Player Management Methods

#### `POST /players/applicant` - createApplicant

- **Authentication**: None (public endpoint)
- **Validation**: ZodValidationPipe with Player.toCreateDto
- **Success Cases**:
  - Returns 201 with created Player entity
  - Creates player in database
  - Emits NewApplication event with player and guardian emails
  - Creates PlayerTeamProfile as applicant
- **Error Cases**:
  - 400 Bad Request when validation fails
  - 400 Bad Request when organization doesn't exist
  - 500 Internal Server Error for unexpected errors

#### `POST /organizations/:organizationId/players/:playerId/apply` - applyToOrganization

- **Authentication**: Requires PlayerUser JWT token
- **Authorization**: PlayerUserGuard, PlayerManageGuard
- **Success Cases**:
  - Returns 204 when application successful
  - Creates PlayerTeamProfile for organization
- **Error Cases**:
  - 400 Bad Request when player already applied
  - 401 Unauthorized when no token provided
  - 403 Forbidden when user cannot manage this player
  - 404 Not Found when organization doesn't exist
  - 500 Internal Server Error for unexpected errors

#### `POST /demo/players` - demoCreateApplicant

- **Authentication**: Requires API key
- **Authorization**: ApiKeyGuard
- **Success Cases**:
  - Returns 204 when demo player created
  - Creates realistic demo player data
- **Error Cases**:
  - 401 Unauthorized when no API key provided
  - 400 Bad Request when organization doesn't exist
  - 500 Internal Server Error for unexpected errors

#### `PATCH /players/:playerId` - update

- **Authentication**: Requires PlayerUser JWT token
- **Authorization**: PlayerUserGuard, PlayerManageGuard
- **Validation**: ZodValidationPipe with Player.fullParser
- **Success Cases**:
  - Returns 200 with updated Player entity
  - Updates player data in database
  - Updates guardian data if provided
- **Error Cases**:
  - 400 Bad Request when validation fails
  - 401 Unauthorized when no token provided
  - 403 Forbidden when user cannot manage this player
  - 500 Internal Server Error for unexpected errors

### 3. Team Management Methods

#### `assignNewTeam(dto: AssignTeamDto, profile: Profile)`

- **Guards**: JwtAuthGuard, ProfileGuard, PermissionsGuard
- **Permissions**: ManagePlayers
- **Success Cases**:
  - Assigns player to team and returns PopulatedPlayer
- **Error Cases**:
  - DomainError → BadRequestException
  - ParsingError → InternalServerErrorException
  - Other errors → InternalServerErrorException

#### `changeStatus(dto: ChangeStatusDto, profile: Profile)`

- **Guards**: JwtAuthGuard, ProfileGuard, PermissionsGuard
- **Permissions**: ManageReviews, ManageEvents
- **Success Cases**:
  - Changes player status and returns PopulatedPlayer
- **Error Cases**:
  - DomainError → BadRequestException
  - ParsingError → InternalServerErrorException
  - Other errors → InternalServerErrorException

#### `removeFromOrganization(organizationId: OrganizationId, playerId: PlayerId)`

- **Guards**: JwtAuthGuard, ProfileGuard, PermissionsGuard
- **Permissions**: ManagePlayers
- **Success Cases**:
  - Removes player from organization and returns playerId
- **Error Cases**:
  - DomainError → BadRequestException
  - ParsingError → InternalServerErrorException
  - Other errors → InternalServerErrorException

#### `removeFromTeam(teamId: TeamId, organizationId: OrganizationId, playerId: PlayerId, profile: Profile)`

- **Guards**: JwtAuthGuard, ProfileGuard, PermissionsGuard
- **Permissions**: ManagePlayers
- **Success Cases**:
  - Removes player from team and returns PopulatedPlayer
- **Error Cases**:
  - DomainError → BadRequestException
  - ParsingError → InternalServerErrorException
  - Other errors → InternalServerErrorException

### 4. Query Methods

#### `playerExists(dto: GetPlayerDto)`

- **Guards**: None (public endpoint with validation pipe)
- **Success Cases**:
  - Returns true if player exists
  - Returns false if player doesn't exist
- **Error Cases**:
  - Any error → InternalServerErrorException

#### `isWithinOrganization(orgId: OrganizationId, playerId: PlayerId)`

- **Guards**: JwtAuthGuard
- **Success Cases**:
  - Returns true if player is in organization
  - Returns false if player is not in organization
- **Error Cases**:
  - DomainError → BadRequestException
  - Other errors → InternalServerErrorException

#### `searchPlayers(orgId: OrganizationId, query: PlayerQueryDto)`

- **Guards**: JwtAuthGuard, PermissionsGuard
- **Permissions**: ManagePlayers
- **Success Cases**:
  - Returns PaginatedPopulatedPlayers
- **Error Cases**:
  - Any error → InternalServerErrorException

#### `getTeamPlayers(teamId: TeamId, orgId: OrganizationId, query: PlayerQueryDto)`

- **Guards**: JwtAuthGuard
- **Permissions**: ManageReviews, ManageEvents
- **Success Cases**:
  - Returns PopulatedPlayer array
- **Error Cases**:
  - Any error → InternalServerErrorException

#### `getCurrentPlayers(user: PlayerUser)`

- **Guards**: JwtAuthGuard, PlayerUserGuard
- **Success Cases**:
  - Returns array of related players
- **Error Cases**:
  - Any error → InternalServerErrorException

## Test Implementation Strategy

### 1. Test Structure Pattern

Each method should have:

- Describe block for the method
- Success case tests
- Error case tests for each error type
- Guard verification (implicitly tested through module setup)

### 2. Mock Setup Pattern

```typescript
const module: TestingModule = await Test.createTestingModule({
  controllers: [PlayerController],
  providers: [
    mockDep(PlayerService, {
      methodName: async () => expectedResult,
    }),
    mockDep(EventEmitter2, {
      emit: jest.fn(),
    }),
  ],
})
  .overrideGuard(JwtAuthGuard)
  .useValue(passingGuard)
  .overrideGuard(PlayerUserGuard)
  .useValue(passingGuard)
  .overrideGuard(PlayerManageGuard)
  .useValue(passingGuard)
  .overrideGuard(ProfileGuard)
  .useValue(passingGuard)
  .overrideGuard(PermissionsGuard)
  .useValue(passingGuard)
  .overrideGuard(ApiKeyGuard)
  .useValue(passingGuard)
  .overrideGuard(PlayerOwnsImageGuard)
  .useValue(passingGuard)
  .compile();
```

### 3. Error Testing Pattern

```typescript
it("throws BadRequestException when service returns DomainError", async () => {
  const module = await createTestModule({
    serviceMock: {
      methodName: async () => new DomainError("Test error"),
    },
  });

  const controller = module.get<PlayerController>(PlayerController);

  await expect(controller.methodName(params)).rejects.toThrow(BadRequestException);
});
```

## Additional Testing Utilities Needed

### 1. New Factory Functions

- `playerUserFactory` - For PlayerUser entities
- `imageAssetFactory` - For ImageAsset entities
- `populatedPlayerFactory` - For PopulatedPlayer entities
- `paginatedResultFactory` - For paginated responses

### 2. Test Helper Functions

- `createTestModule(options)` - Standardized module creation
- `createMockPlayerService(overrides)` - Standardized service mocking
- `expectBadRequest(promise)` - Standardized error assertion
- `expectInternalServerError(promise)` - Standardized error assertion

### 3. Test Data Generators

- Valid DTOs for each endpoint
- Invalid DTOs for validation testing
- Error instances for error case testing

## Implementation Priority

### Phase 1: Foundation Setup

1. **Test Infrastructure**: Set up integration test file with in-memory MongoDB
2. **AppTestService Extensions**: Add missing methods for player-related test data
3. **Factory Extensions**: Create playerUserFactory and imageAssetFactory if needed
4. **Authentication Helpers**: Add JWT token generation for PlayerUser authentication

### Phase 2: Public Endpoints (No Authentication)

1. **POST /players/applicant** - createApplicant (most critical, public endpoint)
2. **POST /players/find** - playerExists (simple query endpoint)

### Phase 3: Player Document Management (PlayerUser Authentication)

1. **GET /players/:playerId/documents/images** - getPlayerDocumentImages
2. **PATCH /players/:playerId/documents/submit** - submitPlayerDocuments
3. **PATCH /players/:playerId/documents/photo/:imageId** - addPlayerPhoto
4. **DELETE /players/:playerId/documents/photo** - removePlayerPhoto
5. **PATCH /players/:playerId/documents/image/:imageId** - addPlayerImageDocument
6. **DELETE /players/:playerId/documents/image/:imageId** - removePlayerImageDocument

### Phase 4: Player Management (PlayerUser Authentication)

1. **POST /organizations/:organizationId/players/:playerId/apply** - applyToOrganization
2. **PATCH /players/:playerId** - update
3. **GET /players/current** - getCurrentPlayers

### Phase 5: Admin/Coach Endpoints (Coach Authentication + Permissions)

1. **POST /players/assign-team** - assignNewTeam
2. **POST /players/change-status** - changeStatus
3. **DELETE /organizations/:organizationId/players/:playerId** - removeFromOrganization
4. **DELETE /teams/:teamId/organizations/:organizationId/players/:playerId** - removeFromTeam
5. **GET /organizations/:organizationId/players/search** - searchPlayers
6. **GET /teams/:teamId/organizations/:organizationId/players** - getTeamPlayers
7. **GET /organizations/:organizationId/players/:playerId/within** - isWithinOrganization

### Phase 6: API Key Endpoints (System Integration)

1. **POST /demo/players** - demoCreateApplicant

Each phase should include:

- Success cases with proper HTTP status codes
- Authentication/authorization error cases (401, 403)
- Validation error cases (400)
- Business logic error cases (400, 404)
- Database verification for state changes
- Event emission verification where applicable

## Detailed Implementation Examples

### Example Integration Test Structure

```typescript
describe("player controller integration", () => {
  let app: INestApplication;
  let testService: AppTestService;
  let mongoClient: DBClient;

  beforeAll(async () => {
    const fakeMongo = await createInMemoryMongoClient();
    mongoClient = fakeMongo.db;

    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [AppModule],
      providers: [],
    })
      .overrideModule(DatabaseModule)
      .useModule({
        module: class TestDatabaseModule {},
        imports: [],
        providers: [
          {
            provide: DATABASE_CONNECTION,
            useFactory: async () => fakeMongo.db,
          },
          {
            provide: DATABASE_CLIENT,
            useFactory: async () => fakeMongo.mongoClient,
          },
        ],
        exports: [DATABASE_CONNECTION, DATABASE_CLIENT],
      })
      .compile();

    app = moduleFixture.createNestApplication();
    await app.init();

    testService = new AppTestService(mongoClient);
  });

  afterEach(async () => {
    // Clear all collections between test cases
    const collections = await mongoClient.collections();
    for (const collection of collections) {
      await collection.deleteMany({});
    }
  });

  afterAll(async () => {
    await app.close();
  });

  describe("GET /players/:playerId/documents/images", () => {
    it("returns player document images when authenticated as player", async () => {
      // Setup test data
      const organization = organizationFactory.create();
      await testService.saveOrganization(organization);

      const player = playerFactory.create(organization.id);
      await testService.savePlayer(player);

      const playerUser = playerUserFactory.create({ playerId: player.id });
      await testService.savePlayerUser(playerUser);

      // Create some test images
      const images = [
        imageAssetFactory.create({ ownerId: playerUser.id }),
        imageAssetFactory.create({ ownerId: playerUser.id }),
      ];
      await testService.saveImageAssets(images);

      // Add images to player documents
      const updatedPlayer = Player.addImageDocuments(
        player,
        images.map((img) => img.id),
      );
      await testService.updatePlayer(updatedPlayer);

      // Generate JWT token for player
      const token = await testService.generatePlayerUserToken(playerUser);

      const response = await request(app.getHttpServer())
        .get(`/players/${player.id}/documents/images`)
        .set("Authorization", `Bearer ${token}`)
        .expect(200);

      expect(response.body).toHaveLength(2);
      expect(response.body[0]).toMatchObject({
        id: images[0].id,
        url: expect.any(String),
      });
    });

    it("returns 401 when no token provided", async () => {
      const playerId = UUID.generate<PlayerId>();

      await request(app.getHttpServer()).get(`/players/${playerId}/documents/images`).expect(401);
    });

    it("returns 403 when user cannot manage this player", async () => {
      // Setup player and different user
      const organization = organizationFactory.create();
      await testService.saveOrganization(organization);

      const player = playerFactory.create(organization.id);
      await testService.savePlayer(player);

      const otherPlayerUser = playerUserFactory.create();
      await testService.savePlayerUser(otherPlayerUser);

      const token = await testService.generatePlayerUserToken(otherPlayerUser);

      await request(app.getHttpServer())
        .get(`/players/${player.id}/documents/images`)
        .set("Authorization", `Bearer ${token}`)
        .expect(403);
    });
  });

  describe("POST /players/applicant", () => {
    it("creates applicant successfully with valid data", async () => {
      // Setup organization
      const organization = organizationFactory.create();
      await testService.saveOrganization(organization);

      const createPlayerDto = {
        organizationId: organization.id,
        firstName: "John",
        lastName: "Doe",
        email: "<EMAIL>",
        phone: "+**********",
        dob: CustomDate.subYears(15),
        gender: { name: PlayerGenders.Male },
        playingExperience: PlayingExperience.Beginner,
        playingExperienceDescription: "New to the sport",
        medicalConditions: "None",
        address: {
          postcode: "SW1A 1AA",
          address: "123 Test Street, London, UK",
        },
        acceptedTerms: true,
      };

      const response = await request(app.getHttpServer())
        .post("/players/applicant")
        .send(createPlayerDto)
        .expect(201);

      // Verify player was created
      expect(response.body).toMatchObject({
        id: expect.any(String),
        firstName: "John",
        lastName: "Doe",
        email: "<EMAIL>",
        organizationId: organization.id,
      });

      // Verify player exists in database
      const savedPlayer = await mongoClient.collection("players").findOne({ id: response.body.id });
      expect(savedPlayer).toBeTruthy();

      // Verify PlayerTeamProfile was created
      const playerProfile = await mongoClient
        .collection("player-team-profiles")
        .findOne({ playerId: response.body.id });
      expect(playerProfile).toBeTruthy();
      expect(playerProfile.status).toBe("applicant");
    });

    it("creates applicant with guardian successfully", async () => {
      const organization = organizationFactory.create();
      await testService.saveOrganization(organization);

      const createPlayerDto = {
        organizationId: organization.id,
        firstName: "Jane",
        lastName: "Smith",
        email: "<EMAIL>",
        phone: "+**********",
        dob: CustomDate.subYears(10), // Under 18, requires guardian
        gender: { name: PlayerGenders.Female },
        playingExperience: PlayingExperience.Beginner,
        playingExperienceDescription: "New to the sport",
        medicalConditions: "None",
        address: {
          postcode: "SW1A 1AA",
          address: "123 Test Street, London, UK",
        },
        acceptedTerms: true,
        guardian: {
          firstName: "Mary",
          lastName: "Smith",
          email: "<EMAIL>",
          phone: "+**********",
        },
      };

      const response = await request(app.getHttpServer())
        .post("/players/applicant")
        .send(createPlayerDto)
        .expect(201);

      // Verify guardian was created
      const savedGuardian = await mongoClient
        .collection("guardians")
        .findOne({ email: "<EMAIL>" });
      expect(savedGuardian).toBeTruthy();
      expect(savedGuardian.firstName).toBe("Mary");
    });

    it("returns 400 when organization doesn't exist", async () => {
      const createPlayerDto = {
        organizationId: UUID.generate<OrganizationId>(),
        firstName: "John",
        lastName: "Doe",
        email: "<EMAIL>",
        // ... other required fields
      };

      await request(app.getHttpServer())
        .post("/players/applicant")
        .send(createPlayerDto)
        .expect(400);
    });

    it("returns 400 when validation fails", async () => {
      const invalidDto = {
        firstName: "", // Invalid: empty string
        lastName: "Doe",
        email: "invalid-email", // Invalid: not an email
        // Missing required fields
      };

      await request(app.getHttpServer()).post("/players/applicant").send(invalidDto).expect(400);
    });
  });
});
```

## Entity Relationship Testing Considerations

### 1. Player-Organization Relationships

- Test player assignment to organizations
- Test player removal from organizations
- Test organization membership validation

### 2. Player-Team Relationships

- Test team assignment and removal
- Test team status changes
- Test team-specific queries

### 3. Player-Guardian Relationships

- Test guardian creation and updates
- Test guardian-player associations
- Test guardian permissions

### 4. Player-Document Relationships

- Test document uploads and associations
- Test document removal
- Test document submission workflows

## Mock Data Patterns

### 1. Consistent ID Generation

```typescript
const testIds = {
  playerId: UUID.generate<PlayerId>(),
  organizationId: UUID.generate<OrganizationId>(),
  teamId: UUID.generate<TeamId>(),
  profileId: UUID.generate<ProfileId>(),
  imageId: UUID.generate<Assets.Image.ImageId>(),
};
```

### 2. Factory Usage Patterns

```typescript
const mockPlayer = playerFactory.create(testIds.organizationId);
const mockOrganization = organizationFactory.create();
const mockTeam = teamFactory.create(testIds.organizationId);
const mockProfile = profileFactory.create(UUID.generate<CoachUserId>());
```

### 3. Error Instance Creation

```typescript
const mockErrors = {
  domain: new DomainError("Test domain error"),
  unexpected: new UnexpectedError("Test unexpected error"),
  parsing: new ParsingError("Test parsing error"),
};
```

## Integration Testing Best Practices

### 1. Test Isolation and Data Management

- Each test should be independent with clean database state
- Use `afterEach` to clear all MongoDB collections between tests
- Create fresh test data for each test using factories
- Avoid test interdependencies

### 2. Comprehensive HTTP Testing

- Test all HTTP status codes (200, 201, 400, 401, 403, 404, 500)
- Test request validation with invalid payloads
- Test authentication and authorization scenarios
- Test edge cases and boundary conditions

### 3. Database Verification

- Verify data persistence in MongoDB after operations
- Check entity relationships are properly created
- Validate business logic effects on database state
- Test data consistency across collections

### 4. Authentication and Authorization Testing

- Test endpoints with valid JWT tokens
- Test endpoints without authentication (401 responses)
- Test endpoints with insufficient permissions (403 responses)
- Test player vs guardian access scenarios
- Test cross-player access restrictions

### 5. Event System Testing

- Verify events are emitted with correct data
- Test event payloads match expected structure
- Validate email notifications are triggered appropriately

### 6. Readable Test Structure

- Use descriptive test names following HTTP method and endpoint pattern
- Group tests by endpoint in describe blocks
- Include setup comments explaining test data creation
- Use meaningful variable names for test entities

## Performance and Reliability Considerations

### 1. In-Memory Database Efficiency

- Use mongodb-memory-server for fast, isolated testing
- Leverage existing createInMemoryMongoClient utility
- Ensure proper cleanup to prevent memory leaks

### 2. Test Execution Speed

- Keep test data minimal but realistic
- Use factories for consistent entity creation
- Avoid unnecessary HTTP requests in setup

### 3. Test Reliability

- Use deterministic test data (avoid random values in assertions)
- Handle async operations properly with await
- Use proper error handling in test setup/teardown

### 4. Debugging and Maintenance

- Include helpful error messages in assertions
- Log important test data when debugging
- Keep test data creation methods reusable
- Document complex test scenarios
