- you MUST always read `.ai-assets/docs/agent-instructions.md` before doing any work.
- you MUST always read `.ai-assets/overview.md` to get a comprehensive overview of the project, architecture, and monorepo structure.

- When working in a remote agent environment, you must:
  1. Check the `.nvmrc` file for the correct Node.js version and use it.
  2. Run `npm install` to install dependencies.
  3. Run diagnostics (`lint`, `type-check`, `build`, `test`) using `npx nx`.
